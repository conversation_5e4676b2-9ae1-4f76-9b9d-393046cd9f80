using System.Collections;
using UnityEngine;
using UnityEngine.AI;

public class Enemy : MonoBehaviour
{
    private NavMeshAgent navMeshAgent;
    private Transform playerTransform;
    private Health health;
    private Animator animator;
    [SerializeField] private float stoppingDistance = 1f;
    [SerializeField] private float attackDelay = 1f;

    [SerializeField] private float max_health = 10f;
    [SerializeField] private float current_health = 0f;

    void Awake()
    {
        current_health = max_health;
        navMeshAgent = GetComponent<NavMeshAgent>();
        playerTransform = GameObject.FindWithTag("Player").transform;
        health = playerTransform.GetComponent<Health>();
        animator = GetComponent<Animator>();
    }

    void Update()
    {
        if (navMeshAgent.isOnNavMesh && navMeshAgent.destination != playerTransform.position && Vector3.Distance(transform.position, playerTransform.position) > stoppingDistance) navMeshAgent.destination = playerTransform.position;

        if (navMeshAgent.velocity.magnitude > 0) animator.SetFloat("Speed", 1);
        else animator.SetFloat("Speed", 0);

        if (navMeshAgent.remainingDistance <= stoppingDistance)
        {
            animator.SetLayerWeight(1, 1);
            IEnumerator attack = Attack();
            StartCoroutine(attack);
        }
        else animator.SetLayerWeight(1, 0);
    }

    void OnCollisionEnter(Collision collision)
    {
        if(collision.gameObject.CompareTag("Bullet"))
        {
            TakeDamage(10);
        }
    }

    public void TakeDamage(float damage)
    {
        current_health -= damage;
        if (current_health <= 0) Die();
    }

    IEnumerator Attack()
    {
        if(health != null) health.TakeDamage(10);
        yield return new WaitForSeconds(attackDelay);
    }

    void Die()
    {
        Destroy(gameObject);
    }   
}
