Using pre-set license
Built from '6000.0/release' branch; Version is '6000.0.43f1 (97272b72f107) revision 9905963'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 14229 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.43f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
S:/Unity Projects/Twistoria
-logFile
Logs/AssetImportWorker0.log
-srvPort
49601
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: S:/Unity Projects/Twistoria
S:/Unity Projects/Twistoria
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [25804]  Target information:

Player connection [25804]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 1636278230 [EditorId] 1636278230 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25804]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1636278230 [EditorId] 1636278230 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25804]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1636278230 [EditorId] 1636278230 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25804] Host joined multi-casting on [***********:54997]...
Player connection [25804] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 4.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.43f1 (97272b72f107)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path S:/Unity Projects/Twistoria/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) Graphics (ID=0x1638)
    Vendor:   ATI
    VRAM:     7114 MB
    Driver:   31.0.21923.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56576
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005897 seconds.
- Loaded All Assemblies, in  0.482 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.361 seconds
Domain Reload Profiling: 841ms
	BeginReloadAssembly (193ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (182ms)
		LoadAssemblies (192ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (175ms)
				TypeCache.ScanAssembly (161ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (361ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (312ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (166ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.986 seconds
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Twistoria
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.987 seconds
Domain Reload Profiling: 1971ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (699ms)
		LoadAssemblies (549ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (258ms)
			TypeCache.Refresh (194ms)
				TypeCache.ScanAssembly (176ms)
			BuildScriptInfoCaches (49ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (987ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (769ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (561ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 2.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 220 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6366 unused Assets / (4.4 MB). Loaded Objects now: 7109.
Memory consumption went from 194.6 MB to 190.2 MB.
Total: 20.918200 ms (FindLiveObjects: 1.687300 ms CreateObjectMapping: 1.947700 ms MarkObjects: 11.590600 ms  DeleteObjects: 5.690500 ms)

========================================================================
Received Import Request.
  Time since last request: 518612.602216 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Rifle Aiming Idle.fbx
  artifactKey: Guid(1045c660a25a0a243b307dc5107b5560) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Rifle Aiming Idle.fbx using Guid(1045c660a25a0a243b307dc5107b5560) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ee01c5d2af819695658a34a736fac858') in 0.0432232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 13.142805 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Rifle Aiming Idle.fbx
  artifactKey: Guid(1045c660a25a0a243b307dc5107b5560) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Rifle Aiming Idle.fbx using Guid(1045c660a25a0a243b307dc5107b5560) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '71e8d7c386b19f5011dff85fe5dd8f5f') in 0.0040124 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 137

========================================================================
Received Import Request.
  Time since last request: 8.164712 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Rifle Aiming Idle.fbx
  artifactKey: Guid(1045c660a25a0a243b307dc5107b5560) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Rifle Aiming Idle.fbx using Guid(1045c660a25a0a243b307dc5107b5560) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fcea255894d579cd65d8c8631ff252ff') in 0.0039723 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 137

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.747 seconds
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Error saving file Library/StateCache/LayerSettings/9e/9ecc5c3c162a7aba29bafa64c8b94b7c.json. Error: System.IO.IOException: Sharing violation on path S:\Unity Projects\Twistoria\Library\StateCache\LayerSettings\9e\9ecc5c3c162a7aba29bafa64c8b94b7c.json
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,System.IO.FileOptions)
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding, System.Int32 bufferSize) [0x00055] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.StreamWriter..ctor(string,bool,System.Text.Encoding)
  at System.IO.File.WriteAllText (System.String path, System.String contents, System.Text.Encoding encoding) [0x00034] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at UnityEditor.StateCache`1[T].SetState (UnityEngine.Hash128 key, T obj) [0x0004b] in <098f57384b5148a5a26f4d98e6aa9064>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.StateCache`1<UnityEditor.Tools/LayerSettings>:SetState (UnityEngine.Hash128,UnityEditor.Tools/LayerSettings)
UnityEditor.Tools:set_lockedLayers (int)
UnityEditor.Tools:OnEnable ()
UnityEngine.ScriptableObject:CreateScriptableObjectInstanceFromType (System.Type,bool)
UnityEngine.ScriptableObject:CreateInstance (System.Type)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.Tools> ()
UnityEditor.Tools:get_get ()
UnityEditor.Tools:get_visibleLayers ()
UnityEditor.Animations.Rigging.BoneRendererUtils:.cctor () (at ./Library/PackageCache/com.unity.animation.rigging@68167b505d2b/Editor/Utils/BoneRendererUtils.cs:121)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.animation.rigging@68167b505d2b/Editor/Utils/BoneRendererUtils.cs Line: 121)

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.805 seconds
Domain Reload Profiling: 1554ms
	BeginReloadAssembly (204ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (463ms)
		LoadAssemblies (397ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (145ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (805ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (569ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (408ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6365 unused Assets / (4.6 MB). Loaded Objects now: 7128.
Memory consumption went from 174.9 MB to 170.3 MB.
Total: 16.018700 ms (FindLiveObjects: 1.685800 ms CreateObjectMapping: 1.582100 ms MarkObjects: 8.009800 ms  DeleteObjects: 4.739400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 94.755667 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/CustomStarterAssetsThirdPerson 1.controller
  artifactKey: Guid(e4187edabfd0bab41b78f4a9e6036b27) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/CustomStarterAssetsThirdPerson 1.controller using Guid(e4187edabfd0bab41b78f4a9e6036b27) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '22dd5f8e09cf24529e657d5615f2a1ce') in 0.0339264 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.719 seconds
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.906 seconds
Domain Reload Profiling: 1628ms
	BeginReloadAssembly (188ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (458ms)
		LoadAssemblies (371ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (175ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (152ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (907ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (692ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (501ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6365 unused Assets / (4.4 MB). Loaded Objects now: 7131.
Memory consumption went from 173.2 MB to 168.8 MB.
Total: 12.805500 ms (FindLiveObjects: 1.865800 ms CreateObjectMapping: 0.855700 ms MarkObjects: 6.144100 ms  DeleteObjects: 3.936600 ms)

Prepare: number of updated asset objects reloaded= 0
