{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Cinemachine/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.Timeline": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Cinemachine.dll": {}}, "runtime": {"bin/placeholder/Cinemachine.dll": {}}}, "com.unity.cinemachine.editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Cinemachine": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Editor": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.Timeline": "1.0.0", "Unity.Timeline.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/com.unity.cinemachine.editor.dll": {}}, "runtime": {"bin/placeholder/com.unity.cinemachine.editor.dll": {}}}, "PPv2URPConverters/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Editor": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/PPv2URPConverters.dll": {}}, "runtime": {"bin/placeholder/PPv2URPConverters.dll": {}}}, "Unity.AI.Navigation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.dll": {}}}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}}, "Unity.AI.Navigation.Updater/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Updater.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Updater.dll": {}}}, "Unity.Animation.Rigging/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Animation.Rigging.dll": {}}, "runtime": {"bin/placeholder/Unity.Animation.Rigging.dll": {}}}, "Unity.Animation.Rigging.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Animation.Rigging": "1.0.0", "Unity.Burst": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Animation.Rigging.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Animation.Rigging.Editor.dll": {}}}, "Unity.Burst/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.dll": {}}}, "Unity.Burst.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.Editor.dll": {}}}, "Unity.Collections/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.dll": {}}}, "Unity.Collections.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Collections": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.Editor.dll": {}}}, "Unity.InputSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.dll": {}}}, "Unity.InputSystem.ForUI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.ForUI.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.ForUI.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}}, "Unity.Multiplayer.Center.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Multiplayer.Center.Common.dll": {}}, "runtime": {"bin/placeholder/Unity.Multiplayer.Center.Common.dll": {}}}, "Unity.Multiplayer.Center.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Multiplayer.Center.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Multiplayer.Center.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Multiplayer.Center.Editor.dll": {}}}, "Unity.PackageValidationSuite.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.PackageValidationSuite.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.PackageValidationSuite.Editor.dll": {}}}, "Unity.PackageValidationSuite.Editor.Extension/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.PackageValidationSuite.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.PackageValidationSuite.Editor.Extension.dll": {}}, "runtime": {"bin/placeholder/Unity.PackageValidationSuite.Editor.Extension.dll": {}}}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}}, "Unity.Rendering.LightTransport.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Editor.dll": {}}}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.Rendering.LightTransport.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst.Editor": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor.Shared": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.ShaderGraph.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Shaders/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Shaders.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Shaders.dll": {}}}, "Unity.Rider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rider.Editor.dll": {}}}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.ShaderGraph.Utilities": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}}, "Unity.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.dll": {}}}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.TextMeshPro": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}}, "Unity.Timeline/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.dll": {}}}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Timeline": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}}, "Unity.VisualScripting.SettingsProvider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.SettingsProvider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.SettingsProvider.Editor.dll": {}}}, "Unity.VisualScripting.Shared.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "Unity.VisualScripting.State.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Shared.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Shared.Editor.dll": {}}}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}}, "Unity.VisualScripting.State.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.Editor.dll": {}}}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"Cinemachine/1.0.0": {"type": "project", "path": "Cinemachine.csproj", "msbuildProject": "Cinemachine.csproj"}, "com.unity.cinemachine.editor/1.0.0": {"type": "project", "path": "com.unity.cinemachine.editor.csproj", "msbuildProject": "com.unity.cinemachine.editor.csproj"}, "PPv2URPConverters/1.0.0": {"type": "project", "path": "PPv2URPConverters.csproj", "msbuildProject": "PPv2URPConverters.csproj"}, "Unity.AI.Navigation/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.csproj", "msbuildProject": "Unity.AI.Navigation.csproj"}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.csproj"}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.ConversionSystem.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "Unity.AI.Navigation.Updater/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Updater.csproj", "msbuildProject": "Unity.AI.Navigation.Updater.csproj"}, "Unity.Animation.Rigging/1.0.0": {"type": "project", "path": "Unity.Animation.Rigging.csproj", "msbuildProject": "Unity.Animation.Rigging.csproj"}, "Unity.Animation.Rigging.Editor/1.0.0": {"type": "project", "path": "Unity.Animation.Rigging.Editor.csproj", "msbuildProject": "Unity.Animation.Rigging.Editor.csproj"}, "Unity.Burst/1.0.0": {"type": "project", "path": "Unity.Burst.csproj", "msbuildProject": "Unity.Burst.csproj"}, "Unity.Burst.Editor/1.0.0": {"type": "project", "path": "Unity.Burst.Editor.csproj", "msbuildProject": "Unity.Burst.Editor.csproj"}, "Unity.Collections/1.0.0": {"type": "project", "path": "Unity.Collections.csproj", "msbuildProject": "Unity.Collections.csproj"}, "Unity.Collections.Editor/1.0.0": {"type": "project", "path": "Unity.Collections.Editor.csproj", "msbuildProject": "Unity.Collections.Editor.csproj"}, "Unity.InputSystem/1.0.0": {"type": "project", "path": "Unity.InputSystem.csproj", "msbuildProject": "Unity.InputSystem.csproj"}, "Unity.InputSystem.ForUI/1.0.0": {"type": "project", "path": "Unity.InputSystem.ForUI.csproj", "msbuildProject": "Unity.InputSystem.ForUI.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "path": "Unity.Mathematics.Editor.csproj", "msbuildProject": "Unity.Mathematics.Editor.csproj"}, "Unity.Multiplayer.Center.Common/1.0.0": {"type": "project", "path": "Unity.Multiplayer.Center.Common.csproj", "msbuildProject": "Unity.Multiplayer.Center.Common.csproj"}, "Unity.Multiplayer.Center.Editor/1.0.0": {"type": "project", "path": "Unity.Multiplayer.Center.Editor.csproj", "msbuildProject": "Unity.Multiplayer.Center.Editor.csproj"}, "Unity.PackageValidationSuite.Editor/1.0.0": {"type": "project", "path": "Unity.PackageValidationSuite.Editor.csproj", "msbuildProject": "Unity.PackageValidationSuite.Editor.csproj"}, "Unity.PackageValidationSuite.Editor.Extension/1.0.0": {"type": "project", "path": "Unity.PackageValidationSuite.Editor.Extension.csproj", "msbuildProject": "Unity.PackageValidationSuite.Editor.Extension.csproj"}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "path": "Unity.PlasticSCM.Editor.csproj", "msbuildProject": "Unity.PlasticSCM.Editor.csproj"}, "Unity.Rendering.LightTransport.Editor/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Editor.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Editor.csproj"}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Runtime.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Runtime.csproj"}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.csproj"}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.csproj"}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.GPUDriven.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.2D.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Config.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Editor.csproj"}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Shaders/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Shaders.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Shaders.csproj"}, "Unity.Rider.Editor/1.0.0": {"type": "project", "path": "Unity.Rider.Editor.csproj", "msbuildProject": "Unity.Rider.Editor.csproj"}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "path": "Unity.Searcher.Editor.csproj", "msbuildProject": "Unity.Searcher.Editor.csproj"}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Editor.csproj", "msbuildProject": "Unity.ShaderGraph.Editor.csproj"}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Utilities.csproj", "msbuildProject": "Unity.ShaderGraph.Utilities.csproj"}, "Unity.TextMeshPro/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.csproj", "msbuildProject": "Unity.TextMeshPro.csproj"}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.Editor.csproj", "msbuildProject": "Unity.TextMeshPro.Editor.csproj"}, "Unity.Timeline/1.0.0": {"type": "project", "path": "Unity.Timeline.csproj", "msbuildProject": "Unity.Timeline.csproj"}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "path": "Unity.Timeline.Editor.csproj", "msbuildProject": "Unity.Timeline.Editor.csproj"}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.csproj", "msbuildProject": "Unity.VisualScripting.Core.csproj"}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Core.Editor.csproj"}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.csproj", "msbuildProject": "Unity.VisualScripting.Flow.csproj"}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Flow.Editor.csproj"}, "Unity.VisualScripting.SettingsProvider.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.SettingsProvider.Editor.csproj", "msbuildProject": "Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "Unity.VisualScripting.Shared.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Shared.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Shared.Editor.csproj"}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.csproj", "msbuildProject": "Unity.VisualScripting.State.csproj"}, "Unity.VisualScripting.State.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.Editor.csproj", "msbuildProject": "Unity.VisualScripting.State.Editor.csproj"}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "path": "Unity.VisualStudio.Editor.csproj", "msbuildProject": "Unity.VisualStudio.Editor.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Cinemachine >= 1.0.0", "PPv2URPConverters >= 1.0.0", "Unity.AI.Navigation >= 1.0.0", "Unity.AI.Navigation.Editor >= 1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem >= 1.0.0", "Unity.AI.Navigation.Updater >= 1.0.0", "Unity.Animation.Rigging >= 1.0.0", "Unity.Animation.Rigging.Editor >= 1.0.0", "Unity.Burst >= 1.0.0", "Unity.Burst.Editor >= 1.0.0", "Unity.Collections >= 1.0.0", "Unity.Collections.Editor >= 1.0.0", "Unity.InputSystem >= 1.0.0", "Unity.InputSystem.ForUI >= 1.0.0", "Unity.Mathematics >= 1.0.0", "Unity.Mathematics.Editor >= 1.0.0", "Unity.Multiplayer.Center.Common >= 1.0.0", "Unity.Multiplayer.Center.Editor >= 1.0.0", "Unity.PackageValidationSuite.Editor >= 1.0.0", "Unity.PackageValidationSuite.Editor.Extension >= 1.0.0", "Unity.PlasticSCM.Editor >= 1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.Core.Editor >= 1.0.0", "Unity.RenderPipelines.Core.Editor.Shared >= 1.0.0", "Unity.RenderPipelines.Core.Runtime >= 1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared >= 1.0.0", "Unity.RenderPipelines.Core.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime >= 1.0.0", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary >= 1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Editor >= 1.0.0", "Unity.RenderPipelines.Universal.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Shaders >= 1.0.0", "Unity.Rendering.LightTransport.Editor >= 1.0.0", "Unity.Rendering.LightTransport.Runtime >= 1.0.0", "Unity.Rider.Editor >= 1.0.0", "Unity.Searcher.Editor >= 1.0.0", "Unity.ShaderGraph.Editor >= 1.0.0", "Unity.TextMeshPro >= 1.0.0", "Unity.TextMeshPro.Editor >= 1.0.0", "Unity.Timeline >= 1.0.0", "Unity.Timeline.Editor >= 1.0.0", "Unity.VisualScripting.Core >= 1.0.0", "Unity.VisualScripting.Core.Editor >= 1.0.0", "Unity.VisualScripting.Flow >= 1.0.0", "Unity.VisualScripting.Flow.Editor >= 1.0.0", "Unity.VisualScripting.SettingsProvider.Editor >= 1.0.0", "Unity.VisualScripting.Shared.Editor >= 1.0.0", "Unity.VisualScripting.State >= 1.0.0", "Unity.VisualScripting.State.Editor >= 1.0.0", "Unity.VisualStudio.Editor >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.UI >= 1.0.0", "com.unity.cinemachine.editor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Cinemachine.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj"}, "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj"}, "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}