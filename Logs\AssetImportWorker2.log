Using pre-set license
Built from '6000.0/release' branch; Version is '6000.0.43f1 (97272b72f107) revision 9905963'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 14229 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.43f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
S:/Unity Projects/Twistoria
-logFile
Logs/AssetImportWorker2.log
-srvPort
51176
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: S:/Unity Projects/Twistoria
S:/Unity Projects/Twistoria
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [23276]  Target information:

Player connection [23276]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 720507696 [EditorId] 720507696 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [23276]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 720507696 [EditorId] 720507696 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [23276]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 720507696 [EditorId] 720507696 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [23276] Host joined multi-casting on [***********:54997]...
Player connection [23276] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 4.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.43f1 (97272b72f107)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path S:/Unity Projects/Twistoria/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) Graphics (ID=0x1638)
    Vendor:   ATI
    VRAM:     7114 MB
    Driver:   31.0.21923.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56000
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003256 seconds.
- Loaded All Assemblies, in  0.420 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.319 seconds
Domain Reload Profiling: 738ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (153ms)
		LoadAssemblies (155ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (150ms)
			TypeCache.Refresh (149ms)
				TypeCache.ScanAssembly (136ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (320ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (275ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (49ms)
			ProcessInitializeOnLoadAttributes (144ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.766 seconds
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Error saving file Library/StateCache/LayerSettings/9e/9ecc5c3c162a7aba29bafa64c8b94b7c.json. Error: System.IO.IOException: Sharing violation on path S:\Unity Projects\Twistoria\Library\StateCache\LayerSettings\9e\9ecc5c3c162a7aba29bafa64c8b94b7c.json
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,System.IO.FileOptions)
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding, System.Int32 bufferSize) [0x00055] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.StreamWriter..ctor(string,bool,System.Text.Encoding)
  at System.IO.File.WriteAllText (System.String path, System.String contents, System.Text.Encoding encoding) [0x00034] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at UnityEditor.StateCache`1[T].SetState (UnityEngine.Hash128 key, T obj) [0x0004b] in <098f57384b5148a5a26f4d98e6aa9064>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.StateCache`1<UnityEditor.Tools/LayerSettings>:SetState (UnityEngine.Hash128,UnityEditor.Tools/LayerSettings)
UnityEditor.Tools:set_lockedLayers (int)
UnityEditor.Tools:OnEnable ()
UnityEngine.ScriptableObject:CreateScriptableObjectInstanceFromType (System.Type,bool)
UnityEngine.ScriptableObject:CreateInstance (System.Type)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.Tools> ()
UnityEditor.Tools:get_get ()
UnityEditor.Tools:get_visibleLayers ()
UnityEditor.Animations.Rigging.BoneRendererUtils:.cctor () (at ./Library/PackageCache/com.unity.animation.rigging@68167b505d2b/Editor/Utils/BoneRendererUtils.cs:121)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.animation.rigging@68167b505d2b/Editor/Utils/BoneRendererUtils.cs Line: 121)

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Twistoria
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.785 seconds
Domain Reload Profiling: 1547ms
	BeginReloadAssembly (171ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (507ms)
		LoadAssemblies (364ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (240ms)
			TypeCache.Refresh (182ms)
				TypeCache.ScanAssembly (166ms)
			BuildScriptInfoCaches (45ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (786ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (613ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (435ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 220 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6366 unused Assets / (4.5 MB). Loaded Objects now: 7109.
Memory consumption went from 192.5 MB to 188.0 MB.
Total: 11.337100 ms (FindLiveObjects: 0.777800 ms CreateObjectMapping: 0.975800 ms MarkObjects: 5.898800 ms  DeleteObjects: 3.683300 ms)

========================================================================
Received Import Request.
  Time since last request: 524770.698533 seconds.
  path: Assets/Low Poly Weapons VOL.1/Prefabs/Flash.prefab
  artifactKey: Guid(c0f110e4459b675489b7db1e686d5bc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Prefabs/Flash.prefab using Guid(c0f110e4459b675489b7db1e686d5bc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eceb83cd9ed10688992db5528789e443') in 0.5282373 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 12.777722 seconds.
  path: Assets/Low Poly Weapons VOL.1/Gun_MAT.mat
  artifactKey: Guid(28f0b13c7e9707746b5e0b3336181560) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Gun_MAT.mat using Guid(28f0b13c7e9707746b5e0b3336181560) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fc226a3109653fcde3cbcdc49a714e91') in 0.0410184 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 4.208005 seconds.
  path: Assets/Low Poly Weapons VOL.1/Gun_MAT.mat
  artifactKey: Guid(28f0b13c7e9707746b5e0b3336181560) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Gun_MAT.mat using Guid(28f0b13c7e9707746b5e0b3336181560) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd097abf50794609c3193774a84aa5513') in 0.0188926 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1.898556 seconds.
  path: Assets/Low Poly Weapons VOL.1/Prefabs/AK74.prefab
  artifactKey: Guid(ee329c3cd08e58645834d20702d7f6c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Prefabs/AK74.prefab using Guid(ee329c3cd08e58645834d20702d7f6c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'deabea63598258db74dd0e66303f4d3a') in 0.0197955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Low Poly Weapons VOL.1/Prefabs/Uzi.prefab
  artifactKey: Guid(6273e2936e9d6a24e9f64c7c70d6bd19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Prefabs/Uzi.prefab using Guid(6273e2936e9d6a24e9f64c7c70d6bd19) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c24b3d9d4fd6a769c4721dc632ffe2dd') in 0.0212839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Low Poly Weapons VOL.1/Prefabs/Smoke.prefab
  artifactKey: Guid(8b4cba336c093ff41ae9321f9f8433d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Prefabs/Smoke.prefab using Guid(8b4cba336c093ff41ae9321f9f8433d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ca07fddbd0f710f7142d22187405fe46') in 0.0207213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Low Poly Weapons VOL.1/Prefabs/M1911.prefab
  artifactKey: Guid(6f1a7354265853f44b0ed5e2e43676ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Prefabs/M1911.prefab using Guid(6f1a7354265853f44b0ed5e2e43676ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5ae20e746a80541d3852f4d8bcd4a337') in 0.0212563 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.719 seconds
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Error loading file Library/StateCache/LayerSettings/9e/9ecc5c3c162a7aba29bafa64c8b94b7c.json. Error: System.IO.IOException: Sharing violation on path S:\Unity Projects\Twistoria\Library\StateCache\LayerSettings\9e\9ecc5c3c162a7aba29bafa64c8b94b7c.json
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,System.IO.FileOptions)
  at System.IO.StreamReader..ctor (System.String path, System.Text.Encoding encoding, System.Boolean detectEncodingFromByteOrderMarks, System.Int32 bufferSize) [0x00055] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.StreamReader..ctor (System.String path, System.Text.Encoding encoding, System.Boolean detectEncodingFromByteOrderMarks) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.StreamReader..ctor(string,System.Text.Encoding,bool)
  at System.IO.File.InternalReadAllText (System.String path, System.Text.Encoding encoding) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.File.ReadAllText (System.String path, System.Text.Encoding encoding) [0x00034] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at UnityEditor.StateCache`1[T].GetState (UnityEngine.Hash128 key, T defaultValue) [0x00041] in <098f57384b5148a5a26f4d98e6aa9064>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.StateCache`1<UnityEditor.Tools/LayerSettings>:GetState (UnityEngine.Hash128,UnityEditor.Tools/LayerSettings)
UnityEditor.Tools:OnEnable ()
UnityEngine.ScriptableObject:CreateScriptableObjectInstanceFromType (System.Type,bool)
UnityEngine.ScriptableObject:CreateInstance (System.Type)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.Tools> ()
UnityEditor.Tools:get_get ()
UnityEditor.Tools:get_visibleLayers ()
UnityEditor.Animations.Rigging.BoneRendererUtils:.cctor () (at ./Library/PackageCache/com.unity.animation.rigging@68167b505d2b/Editor/Utils/BoneRendererUtils.cs:121)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.animation.rigging@68167b505d2b/Editor/Utils/BoneRendererUtils.cs Line: 121)

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.809 seconds
Domain Reload Profiling: 1529ms
	BeginReloadAssembly (219ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (431ms)
		LoadAssemblies (347ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (175ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (154ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (809ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (604ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (440ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6366 unused Assets / (4.6 MB). Loaded Objects now: 7191.
Memory consumption went from 187.1 MB to 182.5 MB.
Total: 11.727200 ms (FindLiveObjects: 1.013600 ms CreateObjectMapping: 1.003800 ms MarkObjects: 6.596800 ms  DeleteObjects: 3.111700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 156.727564 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border
  artifactKey: Guid(84275cdf81c4e5a4488f79471ecb48c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border using Guid(84275cdf81c4e5a4488f79471ecb48c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '606752db8133f41c973536f78e98f0ec') in 0.0023929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.359952 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-001.png
  artifactKey: Guid(f6db504923c10194190db78073cbcc82) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-001.png using Guid(f6db504923c10194190db78073cbcc82) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8ccf757364c8577cf36c3a7463561cb8') in 0.0602344 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-004.png
  artifactKey: Guid(0af3268ad82d8a94fbb62c771cfefc1a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-004.png using Guid(0af3268ad82d8a94fbb62c771cfefc1a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fb6a63783d8e7deeb1b5e0dcdb58bed6') in 0.0155744 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-014.png
  artifactKey: Guid(40c9c58c7d2783c4c878cd82d09aab46) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-014.png using Guid(40c9c58c7d2783c4c878cd82d09aab46) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5610f88bc78db11432119ef11ccca223') in 0.0153827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-005.png
  artifactKey: Guid(b391bec24842eef4cbf434127553cda0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-005.png using Guid(b391bec24842eef4cbf434127553cda0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e79b8c4dbe22f1fadc1d54e24c2bd072') in 0.0155286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-025.png
  artifactKey: Guid(7a05a22a527c7144c98eeb5c1b77cb79) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-025.png using Guid(7a05a22a527c7144c98eeb5c1b77cb79) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '47cd3548029d22765546bc661d11dcf5') in 0.0138149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-029.png
  artifactKey: Guid(79cae5181414c9843ad87bd444259052) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-029.png using Guid(79cae5181414c9843ad87bd444259052) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a9dae4485fb046f6bf8e98e960ab0e7a') in 0.0191067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-027.png
  artifactKey: Guid(a0179c603db31b84c8c3df602a60362e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-027.png using Guid(a0179c603db31b84c8c3df602a60362e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '129591eb5d79b8d25c85d113f53484a5') in 0.0248741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-010.png
  artifactKey: Guid(472f028ee9476354bb5d0081033c1643) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-010.png using Guid(472f028ee9476354bb5d0081033c1643) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a69bd59b9cfef609e0f8fa0604635d5') in 0.0182009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-013.png
  artifactKey: Guid(436ca35e3065b43439e7e5e0c139e69d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-013.png using Guid(436ca35e3065b43439e7e5e0c139e69d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f9f7fd39f50cededd740ed6f37b6c2ae') in 0.0194781 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-030.png
  artifactKey: Guid(8a1732b00f399e242812f1fcdea79ce2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-030.png using Guid(8a1732b00f399e242812f1fcdea79ce2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4774d4ebd3d844514f89bb26f0e0696c') in 0.0197008 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-016.png
  artifactKey: Guid(03a64876b8e9cab4b9054b97c44198fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-016.png using Guid(03a64876b8e9cab4b9054b97c44198fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '13ee33338e9a0a184cab9df1ee762754') in 0.0197089 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-012.png
  artifactKey: Guid(95eecabf974886b4ab13359c18c05126) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-012.png using Guid(95eecabf974886b4ab13359c18c05126) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '67e587b6af46ae3644275721cf51ff6c') in 0.0198509 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-017.png
  artifactKey: Guid(81dcaa093adad974aa823aa60556d287) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-017.png using Guid(81dcaa093adad974aa823aa60556d287) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd02c765312180af343747206ad0ba89e') in 0.0196062 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 27.098586 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-002.png
  artifactKey: Guid(702f1d3b4bb3c574dba6f4e80a113c03) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-002.png using Guid(702f1d3b4bb3c574dba6f4e80a113c03) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b412b83001931286255becedc71bc339') in 0.0182457 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-019.png
  artifactKey: Guid(d017c6edf8b49f64e89635b0b49a0ce3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI/kenney_fantasy-ui-borders/PNG/Double/Transparent border/panel-transparent-border-019.png using Guid(d017c6edf8b49f64e89635b0b49a0ce3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a7182e7177315a826b063b0d9e1edf29') in 0.0191696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0