Using pre-set license
Built from '6000.0/release' branch; Version is '6000.0.43f1 (97272b72f107) revision 9905963'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 14229 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.43f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
S:/Unity Projects/Twistoria
-logFile
Logs/AssetImportWorker2.log
-srvPort
62451
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: S:/Unity Projects/Twistoria
S:/Unity Projects/Twistoria
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19880]  Target information:

Player connection [19880]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 2851061316 [EditorId] 2851061316 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19880]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2851061316 [EditorId] 2851061316 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19880]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2851061316 [EditorId] 2851061316 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19880] Host joined multi-casting on [***********:54997]...
Player connection [19880] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 176.22 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.43f1 (97272b72f107)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path S:/Unity Projects/Twistoria/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) Graphics (ID=0x1638)
    Vendor:   ATI
    VRAM:     7114 MB
    Driver:   31.0.21923.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56308
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003085 seconds.
- Loaded All Assemblies, in  1.051 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.375 seconds
Domain Reload Profiling: 1425ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (754ms)
		LoadAssemblies (188ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (751ms)
			TypeCache.Refresh (748ms)
				TypeCache.ScanAssembly (730ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (376ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (324ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (164ms)
			ProcessInitializeOnLoadMethodAttributes (74ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  1.021 seconds
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Error saving file Library/StateCache/LayerSettings/9e/9ecc5c3c162a7aba29bafa64c8b94b7c.json. Error: System.IO.IOException: Sharing violation on path S:\Unity Projects\Twistoria\Library\StateCache\LayerSettings\9e\9ecc5c3c162a7aba29bafa64c8b94b7c.json
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,System.IO.FileOptions)
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding, System.Int32 bufferSize) [0x00055] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.StreamWriter..ctor(string,bool,System.Text.Encoding)
  at System.IO.File.WriteAllText (System.String path, System.String contents, System.Text.Encoding encoding) [0x00034] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at UnityEditor.StateCache`1[T].SetState (UnityEngine.Hash128 key, T obj) [0x0004b] in <098f57384b5148a5a26f4d98e6aa9064>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.StateCache`1<UnityEditor.Tools/LayerSettings>:SetState (UnityEngine.Hash128,UnityEditor.Tools/LayerSettings)
UnityEditor.Tools:set_lockedLayers (int)
UnityEditor.Tools:OnEnable ()
UnityEngine.ScriptableObject:CreateScriptableObjectInstanceFromType (System.Type,bool)
UnityEngine.ScriptableObject:CreateInstance (System.Type)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.Tools> ()
UnityEditor.Tools:get_get ()
UnityEditor.Tools:get_visibleLayers ()
UnityEditor.Animations.Rigging.BoneRendererUtils:.cctor () (at ./Library/PackageCache/com.unity.animation.rigging@68167b505d2b/Editor/Utils/BoneRendererUtils.cs:121)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.animation.rigging@68167b505d2b/Editor/Utils/BoneRendererUtils.cs Line: 121)

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Twistoria
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.003 seconds
Domain Reload Profiling: 2022ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (745ms)
		LoadAssemblies (536ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (315ms)
			TypeCache.Refresh (251ms)
				TypeCache.ScanAssembly (183ms)
			BuildScriptInfoCaches (48ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1003ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (515ms)
			ProcessInitializeOnLoadMethodAttributes (83ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 227 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6529 unused Assets / (4.6 MB). Loaded Objects now: 7280.
Memory consumption went from 196.4 MB to 191.7 MB.
Total: 9.735900 ms (FindLiveObjects: 0.955400 ms CreateObjectMapping: 0.936800 ms MarkObjects: 5.128200 ms  DeleteObjects: 2.713700 ms)

========================================================================
Received Import Request.
  Time since last request: 439290.173522 seconds.
  path: Assets/Bullet FX Pack Trail, Fire, Energy/Prefabs
  artifactKey: Guid(1ebb58bcfe5ed144d8121d649d91044e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bullet FX Pack Trail, Fire, Energy/Prefabs using Guid(1ebb58bcfe5ed144d8121d649d91044e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4f406bd32871cec96b7b9f599fadeb39') in 0.0072228 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.225983 seconds.
  path: Assets/Bullet FX Pack Trail, Fire, Energy/Prefabs/Bullet.prefab
  artifactKey: Guid(0707898d3f02d8c499685d8871d5031a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bullet FX Pack Trail, Fire, Energy/Prefabs/Bullet.prefab using Guid(0707898d3f02d8c499685d8871d5031a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '29114e84bae87affb31eb6048ba5d36c') in 0.550758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  1.019 seconds
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.345 seconds
Domain Reload Profiling: 2366ms
	BeginReloadAssembly (273ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (653ms)
		LoadAssemblies (441ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (331ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (292ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1346ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1007ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (172ms)
			ProcessInitializeOnLoadAttributes (728ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6530 unused Assets / (4.8 MB). Loaded Objects now: 7329.
Memory consumption went from 188.8 MB to 184.0 MB.
Total: 21.756400 ms (FindLiveObjects: 1.860400 ms CreateObjectMapping: 2.317700 ms MarkObjects: 11.885900 ms  DeleteObjects: 5.690300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.806 seconds
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.870 seconds
Domain Reload Profiling: 1679ms
	BeginReloadAssembly (209ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (514ms)
		LoadAssemblies (410ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (204ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (871ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (648ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (453ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.8 MB). Loaded Objects now: 7332.
Memory consumption went from 186.9 MB to 182.1 MB.
Total: 10.156100 ms (FindLiveObjects: 0.740400 ms CreateObjectMapping: 0.941500 ms MarkObjects: 5.385200 ms  DeleteObjects: 3.087800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.764 seconds
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.881 seconds
Domain Reload Profiling: 1647ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (482ms)
		LoadAssemblies (375ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (204ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (174ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (881ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (686ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (492ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.5 MB). Loaded Objects now: 7335.
Memory consumption went from 187.0 MB to 182.5 MB.
Total: 9.978300 ms (FindLiveObjects: 0.752100 ms CreateObjectMapping: 0.552600 ms MarkObjects: 5.983000 ms  DeleteObjects: 2.689300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.713 seconds
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.878 seconds
Domain Reload Profiling: 1594ms
	BeginReloadAssembly (189ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (450ms)
		LoadAssemblies (346ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (195ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (878ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (664ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (475ms)
			ProcessInitializeOnLoadMethodAttributes (74ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (5.1 MB). Loaded Objects now: 7338.
Memory consumption went from 187.0 MB to 181.9 MB.
Total: 12.258100 ms (FindLiveObjects: 0.878600 ms CreateObjectMapping: 0.929500 ms MarkObjects: 6.849900 ms  DeleteObjects: 3.598700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.715 seconds
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.853 seconds
Domain Reload Profiling: 1569ms
	BeginReloadAssembly (203ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (437ms)
		LoadAssemblies (339ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (853ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (645ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (127ms)
			ProcessInitializeOnLoadAttributes (454ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.6 MB). Loaded Objects now: 7341.
Memory consumption went from 187.0 MB to 182.4 MB.
Total: 9.336800 ms (FindLiveObjects: 0.723100 ms CreateObjectMapping: 0.481300 ms MarkObjects: 5.602800 ms  DeleteObjects: 2.528500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.730 seconds
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.917 seconds
Domain Reload Profiling: 1650ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (446ms)
		LoadAssemblies (364ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (918ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (676ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (474ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.8 MB). Loaded Objects now: 7344.
Memory consumption went from 187.0 MB to 182.2 MB.
Total: 13.883000 ms (FindLiveObjects: 0.884000 ms CreateObjectMapping: 0.784900 ms MarkObjects: 7.326800 ms  DeleteObjects: 4.885000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.869 seconds
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.870 seconds
Domain Reload Profiling: 1741ms
	BeginReloadAssembly (255ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (522ms)
		LoadAssemblies (431ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (870ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (642ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (136ms)
			ProcessInitializeOnLoadAttributes (438ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 0.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.6 MB). Loaded Objects now: 7347.
Memory consumption went from 187.0 MB to 182.4 MB.
Total: 9.207300 ms (FindLiveObjects: 0.718600 ms CreateObjectMapping: 0.484300 ms MarkObjects: 5.477700 ms  DeleteObjects: 2.525500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.712 seconds
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.820 seconds
Domain Reload Profiling: 1534ms
	BeginReloadAssembly (194ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (442ms)
		LoadAssemblies (340ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (821ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (613ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (433ms)
			ProcessInitializeOnLoadMethodAttributes (69ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.9 MB). Loaded Objects now: 7350.
Memory consumption went from 187.0 MB to 182.1 MB.
Total: 15.119800 ms (FindLiveObjects: 1.412700 ms CreateObjectMapping: 1.255300 ms MarkObjects: 7.671500 ms  DeleteObjects: 4.777500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.743 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.754 seconds
Domain Reload Profiling: 1499ms
	BeginReloadAssembly (201ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (464ms)
		LoadAssemblies (363ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (202ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (755ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (564ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (403ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.7 MB). Loaded Objects now: 7353.
Memory consumption went from 187.0 MB to 182.3 MB.
Total: 17.802700 ms (FindLiveObjects: 1.533100 ms CreateObjectMapping: 1.570900 ms MarkObjects: 9.259600 ms  DeleteObjects: 5.437200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.774 seconds
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.823 seconds
Domain Reload Profiling: 1599ms
	BeginReloadAssembly (216ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (475ms)
		LoadAssemblies (384ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (823ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (615ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (445ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 1.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (5.2 MB). Loaded Objects now: 7356.
Memory consumption went from 187.0 MB to 181.9 MB.
Total: 11.535500 ms (FindLiveObjects: 1.038300 ms CreateObjectMapping: 0.867400 ms MarkObjects: 5.729600 ms  DeleteObjects: 3.898400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.913 seconds
Refreshing native plugins compatible for Editor in 0.82 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.831 seconds
Domain Reload Profiling: 1746ms
	BeginReloadAssembly (204ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (621ms)
		LoadAssemblies (500ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (218ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (831ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (626ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (452ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 1.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.8 MB). Loaded Objects now: 7359.
Memory consumption went from 187.0 MB to 182.2 MB.
Total: 10.470400 ms (FindLiveObjects: 0.827600 ms CreateObjectMapping: 0.868400 ms MarkObjects: 6.022400 ms  DeleteObjects: 2.750900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.713 seconds
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.782 seconds
Domain Reload Profiling: 1496ms
	BeginReloadAssembly (194ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (448ms)
		LoadAssemblies (349ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (782ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (572ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (410ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.6 MB). Loaded Objects now: 7362.
Memory consumption went from 187.0 MB to 182.4 MB.
Total: 15.328600 ms (FindLiveObjects: 1.640800 ms CreateObjectMapping: 1.274100 ms MarkObjects: 7.609900 ms  DeleteObjects: 4.801400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.774 seconds
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.803 seconds
Domain Reload Profiling: 1578ms
	BeginReloadAssembly (239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (446ms)
		LoadAssemblies (376ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (189ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (803ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (609ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (446ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.8 MB). Loaded Objects now: 7365.
Memory consumption went from 187.0 MB to 182.2 MB.
Total: 10.304700 ms (FindLiveObjects: 0.721000 ms CreateObjectMapping: 0.693600 ms MarkObjects: 5.261200 ms  DeleteObjects: 3.627900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.710 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.779 seconds
Domain Reload Profiling: 1491ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (452ms)
		LoadAssemblies (339ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (203ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (779ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (577ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (411ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.8 MB). Loaded Objects now: 7368.
Memory consumption went from 187.0 MB to 182.3 MB.
Total: 13.598700 ms (FindLiveObjects: 0.775100 ms CreateObjectMapping: 1.266300 ms MarkObjects: 7.490600 ms  DeleteObjects: 4.065000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.754 seconds
Refreshing native plugins compatible for Editor in 0.86 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.777 seconds
Domain Reload Profiling: 1534ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (491ms)
		LoadAssemblies (380ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (199ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (777ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (577ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (414ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 0.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.6 MB). Loaded Objects now: 7371.
Memory consumption went from 187.0 MB to 182.4 MB.
Total: 14.731600 ms (FindLiveObjects: 1.206700 ms CreateObjectMapping: 1.768900 ms MarkObjects: 6.747200 ms  DeleteObjects: 5.007300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.724 seconds
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.777 seconds
Domain Reload Profiling: 1503ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (443ms)
		LoadAssemblies (348ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (195ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (777ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (569ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (402ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 1.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (5.2 MB). Loaded Objects now: 7374.
Memory consumption went from 187.0 MB to 181.8 MB.
Total: 12.287300 ms (FindLiveObjects: 1.087000 ms CreateObjectMapping: 1.163000 ms MarkObjects: 6.378700 ms  DeleteObjects: 3.656500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.869 seconds
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.855 seconds
Domain Reload Profiling: 1726ms
	BeginReloadAssembly (226ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (550ms)
		LoadAssemblies (446ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (208ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (174ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (856ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (647ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.5 MB). Loaded Objects now: 7377.
Memory consumption went from 187.1 MB to 182.5 MB.
Total: 9.031400 ms (FindLiveObjects: 0.746500 ms CreateObjectMapping: 0.484500 ms MarkObjects: 5.285700 ms  DeleteObjects: 2.512700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.753 seconds
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.779 seconds
Domain Reload Profiling: 1533ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (484ms)
		LoadAssemblies (388ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (779ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (579ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (415ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.7 MB). Loaded Objects now: 7380.
Memory consumption went from 187.1 MB to 182.4 MB.
Total: 12.379700 ms (FindLiveObjects: 0.782900 ms CreateObjectMapping: 0.904300 ms MarkObjects: 6.808700 ms  DeleteObjects: 3.882700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.852 seconds
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.837 seconds
Domain Reload Profiling: 1690ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (567ms)
		LoadAssemblies (429ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (231ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (194ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (837ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (624ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (449ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 0.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (5.3 MB). Loaded Objects now: 7383.
Memory consumption went from 187.1 MB to 181.8 MB.
Total: 11.589800 ms (FindLiveObjects: 0.836400 ms CreateObjectMapping: 0.753100 ms MarkObjects: 6.879400 ms  DeleteObjects: 3.119700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.762 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.850 seconds
Domain Reload Profiling: 1613ms
	BeginReloadAssembly (199ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (480ms)
		LoadAssemblies (370ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (204ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (851ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (642ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (451ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.9 MB). Loaded Objects now: 7386.
Memory consumption went from 187.1 MB to 182.1 MB.
Total: 9.668600 ms (FindLiveObjects: 0.814800 ms CreateObjectMapping: 0.619700 ms MarkObjects: 5.174600 ms  DeleteObjects: 3.058400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.803 seconds
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.815 seconds
Domain Reload Profiling: 1619ms
	BeginReloadAssembly (224ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (472ms)
		LoadAssemblies (391ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (816ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (603ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (436ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (5.4 MB). Loaded Objects now: 7389.
Memory consumption went from 187.1 MB to 181.7 MB.
Total: 13.481600 ms (FindLiveObjects: 0.944000 ms CreateObjectMapping: 1.965000 ms MarkObjects: 6.450000 ms  DeleteObjects: 4.121100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.744 seconds
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.796 seconds
Domain Reload Profiling: 1542ms
	BeginReloadAssembly (226ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (438ms)
		LoadAssemblies (365ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (796ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (587ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (422ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 1.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (5.1 MB). Loaded Objects now: 7392.
Memory consumption went from 187.1 MB to 181.9 MB.
Total: 11.566100 ms (FindLiveObjects: 0.790000 ms CreateObjectMapping: 0.701200 ms MarkObjects: 5.913800 ms  DeleteObjects: 4.159400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.723 seconds
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Error saving file Library/StateCache/LayerSettings/9e/9ecc5c3c162a7aba29bafa64c8b94b7c.json. Error: System.IO.IOException: Sharing violation on path S:\Unity Projects\Twistoria\Library\StateCache\LayerSettings\9e\9ecc5c3c162a7aba29bafa64c8b94b7c.json
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,System.IO.FileOptions)
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding, System.Int32 bufferSize) [0x00055] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.StreamWriter..ctor(string,bool,System.Text.Encoding)
  at System.IO.File.WriteAllText (System.String path, System.String contents, System.Text.Encoding encoding) [0x00034] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at UnityEditor.StateCache`1[T].SetState (UnityEngine.Hash128 key, T obj) [0x0004b] in <098f57384b5148a5a26f4d98e6aa9064>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.StateCache`1<UnityEditor.Tools/LayerSettings>:SetState (UnityEngine.Hash128,UnityEditor.Tools/LayerSettings)
UnityEditor.Tools:set_lockedLayers (int)
UnityEditor.Tools:OnEnable ()

Error saving file Library/StateCache/LayerSettings/9e/9ecc5c3c162a7aba29bafa64c8b94b7c.json. Error: System.IO.IOException: Sharing violation on path S:\Unity Projects\Twistoria\Library\StateCache\LayerSettings\9e\9ecc5c3c162a7aba29bafa64c8b94b7c.json
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,System.IO.FileOptions)
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding, System.Int32 bufferSize) [0x00055] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding) [0x00000] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at (wrapper remoting-invoke-with-check) System.IO.StreamWriter..ctor(string,bool,System.Text.Encoding)
  at System.IO.File.WriteAllText (System.String path, System.String contents, System.Text.Encoding encoding) [0x00034] in <13c0c460649d4ce49f991e2c222fa635>:0 
  at UnityEditor.StateCache`1[T].SetState (UnityEngine.Hash128 key, T obj) [0x0004b] in <098f57384b5148a5a26f4d98e6aa9064>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.StateCache`1<UnityEditor.Tools/LayerSettings>:SetState (UnityEngine.Hash128,UnityEditor.Tools/LayerSettings)
UnityEditor.Tools:set_lockedLayers (int)
UnityEditor.Tools:OnEnable ()

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.761 seconds
Domain Reload Profiling: 1486ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (449ms)
		LoadAssemblies (352ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (761ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (553ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (390ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (5.6 MB). Loaded Objects now: 7395.
Memory consumption went from 187.1 MB to 181.5 MB.
Total: 12.495500 ms (FindLiveObjects: 0.836000 ms CreateObjectMapping: 0.729900 ms MarkObjects: 6.610600 ms  DeleteObjects: 4.317300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1090.145164 seconds.
  path: Assets/Bullet FX Pack Trail, Fire, Energy/Materials
  artifactKey: Guid(5db96d45591fc274ba159c69a3f5d6e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bullet FX Pack Trail, Fire, Energy/Materials using Guid(5db96d45591fc274ba159c69a3f5d6e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20e4f75773e166616aa2a93de5a2ff55') in 0.0164461 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Bullet FX Pack Trail, Fire, Energy/Materials/Fire3D.mat
  artifactKey: Guid(c9d2000a469d01447b928df556efebf7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bullet FX Pack Trail, Fire, Energy/Materials/Fire3D.mat using Guid(c9d2000a469d01447b928df556efebf7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cdccb9e783bef329a98a922989d1553b') in 0.6069191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Bullet FX Pack Trail, Fire, Energy/Materials/GunMat.mat
  artifactKey: Guid(4a2780bc0ed8cb5499a8b05fdcb73dc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bullet FX Pack Trail, Fire, Energy/Materials/GunMat.mat using Guid(4a2780bc0ed8cb5499a8b05fdcb73dc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c66049b656ea7d7d7998c86b6156eb97') in 0.020476 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Bullet FX Pack Trail, Fire, Energy/Materials/Floor.physicMaterial
  artifactKey: Guid(c6a6ad2197b4da145a9aa50f43cbde71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bullet FX Pack Trail, Fire, Energy/Materials/Floor.physicMaterial using Guid(c6a6ad2197b4da145a9aa50f43cbde71) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd67378906e2986efd3bc4b5070134e9d') in 0.0021909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Bullet FX Pack Trail, Fire, Energy/Sprites/bullet.png
  artifactKey: Guid(1451610997ae6fb46a32f753f4884d48) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bullet FX Pack Trail, Fire, Energy/Sprites/bullet.png using Guid(1451610997ae6fb46a32f753f4884d48) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ce2ee58e38fae7332caf93c37f39330') in 0.0402275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.886 seconds
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.866 seconds
Domain Reload Profiling: 1754ms
	BeginReloadAssembly (244ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (554ms)
		LoadAssemblies (435ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (218ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (191ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (866ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (642ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (454ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (4.5 MB). Loaded Objects now: 7453.
Memory consumption went from 191.6 MB to 187.1 MB.
Total: 13.680800 ms (FindLiveObjects: 1.541800 ms CreateObjectMapping: 1.088200 ms MarkObjects: 5.999200 ms  DeleteObjects: 5.049800 ms)

Prepare: number of updated asset objects reloaded= 0
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0