{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 1472, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 1472, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 1472, "tid": 4277, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 1472, "tid": 4277, "ts": 1753280357757300, "dur": 1486, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 1472, "tid": 4277, "ts": 1753280357771579, "dur": 1153, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 1472, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1472, "tid": 1, "ts": 1753280356934857, "dur": 9816, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1472, "tid": 1, "ts": 1753280356944678, "dur": 93697, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1472, "tid": 1, "ts": 1753280357038388, "dur": 64835, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 1472, "tid": 4277, "ts": 1753280357772736, "dur": 16, "ph": "X", "name": "", "args": {}}, {"pid": 1472, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356922891, "dur": 2629, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356925523, "dur": 804972, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356926770, "dur": 6612, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356933391, "dur": 4119, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356937516, "dur": 378, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356937896, "dur": 11, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356937907, "dur": 28, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356937938, "dur": 30, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356937971, "dur": 25, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356937998, "dur": 30, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938030, "dur": 27, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938063, "dur": 133, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938200, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938240, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938242, "dur": 38, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938284, "dur": 33, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938320, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938322, "dur": 30, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938354, "dur": 24, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938380, "dur": 24, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938406, "dur": 31, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938440, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938473, "dur": 24, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938499, "dur": 30, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938531, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938533, "dur": 31, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938568, "dur": 26, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938596, "dur": 29, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938626, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938627, "dur": 24, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938654, "dur": 27, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938684, "dur": 26, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938713, "dur": 34, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938749, "dur": 27, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938780, "dur": 30, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938813, "dur": 29, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938843, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938846, "dur": 23, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938872, "dur": 31, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938905, "dur": 24, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938933, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938962, "dur": 24, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356938988, "dur": 30, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939021, "dur": 22, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939045, "dur": 24, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939071, "dur": 24, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939097, "dur": 25, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939124, "dur": 25, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939152, "dur": 20, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939174, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939200, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939226, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939251, "dur": 23, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939276, "dur": 47, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939326, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939328, "dur": 37, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939367, "dur": 33, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939402, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939404, "dur": 25, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939430, "dur": 20, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939453, "dur": 29, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939484, "dur": 23, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939509, "dur": 27, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939539, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939568, "dur": 32, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939603, "dur": 24, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939629, "dur": 25, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939656, "dur": 25, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939684, "dur": 23, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939709, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939734, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939735, "dur": 25, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939763, "dur": 30, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939796, "dur": 26, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939826, "dur": 28, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939857, "dur": 21, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939880, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939905, "dur": 24, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939930, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939933, "dur": 31, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939966, "dur": 26, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356939994, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940021, "dur": 23, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940046, "dur": 26, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940075, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940098, "dur": 31, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940132, "dur": 22, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940157, "dur": 23, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940182, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940208, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940234, "dur": 26, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940262, "dur": 29, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940294, "dur": 24, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940321, "dur": 26, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940349, "dur": 30, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940382, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940384, "dur": 33, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940418, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940420, "dur": 42, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940465, "dur": 23, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940491, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940517, "dur": 25, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940544, "dur": 53, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940599, "dur": 28, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940630, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940632, "dur": 30, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940664, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940665, "dur": 25, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940692, "dur": 2, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940694, "dur": 25, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940722, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940746, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940772, "dur": 24, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940798, "dur": 33, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940833, "dur": 28, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940864, "dur": 25, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940894, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940956, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356940985, "dur": 24, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941011, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941037, "dur": 26, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941065, "dur": 24, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941093, "dur": 24, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941119, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941144, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941169, "dur": 21, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941191, "dur": 25, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941219, "dur": 26, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941247, "dur": 21, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941271, "dur": 23, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941297, "dur": 20, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941319, "dur": 40, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941361, "dur": 30, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941394, "dur": 29, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941425, "dur": 26, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941454, "dur": 23, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941480, "dur": 24, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941506, "dur": 27, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941535, "dur": 200, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941740, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941774, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941776, "dur": 25, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941803, "dur": 35, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941841, "dur": 24, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941867, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941891, "dur": 20, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941914, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941939, "dur": 25, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941967, "dur": 25, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356941994, "dur": 27, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942024, "dur": 25, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942051, "dur": 22, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942075, "dur": 22, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942099, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942125, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942148, "dur": 30, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942180, "dur": 20, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942204, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942228, "dur": 37, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942269, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942298, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942300, "dur": 29, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942331, "dur": 23, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942356, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942358, "dur": 35, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942396, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942397, "dur": 30, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942429, "dur": 34, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942465, "dur": 23, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942491, "dur": 28, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942522, "dur": 19, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942543, "dur": 30, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942575, "dur": 19, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942598, "dur": 28, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942628, "dur": 29, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942660, "dur": 20, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942683, "dur": 22, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942707, "dur": 23, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942732, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942756, "dur": 29, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942788, "dur": 30, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942820, "dur": 29, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942852, "dur": 20, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356942876, "dur": 151, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943043, "dur": 3, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943047, "dur": 129, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943179, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943180, "dur": 115, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943305, "dur": 5, "ph": "X", "name": "ProcessMessages 1461", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943312, "dur": 112, "ph": "X", "name": "ReadAsync 1461", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943426, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943428, "dur": 33, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943463, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943465, "dur": 29, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943496, "dur": 25, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943523, "dur": 32, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943558, "dur": 28, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943589, "dur": 24, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943616, "dur": 24, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943642, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943670, "dur": 20, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943694, "dur": 30, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943727, "dur": 34, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943762, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943764, "dur": 29, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943795, "dur": 24, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943821, "dur": 22, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943846, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943873, "dur": 24, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943899, "dur": 26, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943928, "dur": 35, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943964, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943965, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356943992, "dur": 26, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944022, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944048, "dur": 55, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944106, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944107, "dur": 30, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944140, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944166, "dur": 23, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944192, "dur": 25, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944220, "dur": 24, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944246, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944274, "dur": 40, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944317, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944318, "dur": 27, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944347, "dur": 24, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944373, "dur": 21, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944397, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944421, "dur": 26, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944449, "dur": 23, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944474, "dur": 28, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944505, "dur": 23, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944530, "dur": 30, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944562, "dur": 23, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944587, "dur": 28, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944617, "dur": 28, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944647, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944649, "dur": 30, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944682, "dur": 22, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944707, "dur": 28, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944738, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944770, "dur": 23, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944796, "dur": 24, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944822, "dur": 25, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944849, "dur": 22, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944874, "dur": 19, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944896, "dur": 26, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944925, "dur": 30, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944956, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944958, "dur": 22, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944983, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356944985, "dur": 27, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945014, "dur": 28, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945044, "dur": 20, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945066, "dur": 26, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945095, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945129, "dur": 28, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945159, "dur": 25, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945186, "dur": 22, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945211, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945234, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945262, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945285, "dur": 23, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945311, "dur": 31, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945344, "dur": 26, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945372, "dur": 21, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945396, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945425, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945452, "dur": 29, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945483, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945485, "dur": 31, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945519, "dur": 25, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945546, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945571, "dur": 23, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945596, "dur": 39, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945638, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945679, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945681, "dur": 30, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945714, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945716, "dur": 33, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945750, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945752, "dur": 26, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945781, "dur": 27, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945812, "dur": 25, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945840, "dur": 152, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945995, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356945997, "dur": 45, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356946044, "dur": 2, "ph": "X", "name": "ProcessMessages 2068", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356946046, "dur": 26, "ph": "X", "name": "ReadAsync 2068", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356946075, "dur": 27, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356946104, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356946130, "dur": 23, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356946155, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356946183, "dur": 32, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947142, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947267, "dur": 9, "ph": "X", "name": "ProcessMessages 14980", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947276, "dur": 33, "ph": "X", "name": "ReadAsync 14980", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947311, "dur": 25, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947339, "dur": 25, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947366, "dur": 25, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947393, "dur": 25, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947421, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947457, "dur": 43, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947503, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947505, "dur": 51, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947558, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947561, "dur": 40, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947603, "dur": 2, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947606, "dur": 33, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947644, "dur": 30, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947676, "dur": 31, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947709, "dur": 36, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947748, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947750, "dur": 30, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947783, "dur": 25, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947810, "dur": 31, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947842, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947843, "dur": 27, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947872, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947874, "dur": 37, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947913, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947915, "dur": 27, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947944, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947946, "dur": 27, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356947976, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356948003, "dur": 30, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356948036, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356948038, "dur": 5001, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953045, "dur": 3, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953049, "dur": 280, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953331, "dur": 13, "ph": "X", "name": "ProcessMessages 20517", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953346, "dur": 34, "ph": "X", "name": "ReadAsync 20517", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953382, "dur": 106, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953494, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953523, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953525, "dur": 20, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953548, "dur": 23, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953575, "dur": 91, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953668, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953699, "dur": 24, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953726, "dur": 30, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953758, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953760, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953786, "dur": 83, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953872, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953903, "dur": 38, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953944, "dur": 30, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953977, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356953978, "dur": 25, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954006, "dur": 110, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954118, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954156, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954160, "dur": 33, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954195, "dur": 28, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954225, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954250, "dur": 83, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954336, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954369, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954370, "dur": 25, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954397, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954399, "dur": 24, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954427, "dur": 82, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954511, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954558, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954561, "dur": 38, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954602, "dur": 24, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954629, "dur": 75, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954707, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954734, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954758, "dur": 32, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954792, "dur": 37, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954831, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954833, "dur": 70, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954906, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954932, "dur": 26, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954961, "dur": 27, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356954993, "dur": 21, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955016, "dur": 84, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955104, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955134, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955135, "dur": 24, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955161, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955186, "dur": 92, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955279, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955307, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955308, "dur": 31, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955341, "dur": 30, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955374, "dur": 70, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955446, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955477, "dur": 27, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955505, "dur": 30, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955538, "dur": 66, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955607, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955638, "dur": 26, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955667, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955668, "dur": 33, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955705, "dur": 27, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955734, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955758, "dur": 90, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955852, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955884, "dur": 21, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955908, "dur": 32, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955942, "dur": 28, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955972, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356955973, "dur": 26, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956001, "dur": 25, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956028, "dur": 23, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956053, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956076, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956118, "dur": 74, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956195, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956225, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956251, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956275, "dur": 76, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956355, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956386, "dur": 22, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956410, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956412, "dur": 27, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956441, "dur": 23, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956470, "dur": 21, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956493, "dur": 28, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956524, "dur": 24, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956550, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956551, "dur": 26, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956580, "dur": 22, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956605, "dur": 86, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956693, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956721, "dur": 25, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956748, "dur": 30, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956781, "dur": 23, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956806, "dur": 73, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956881, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956907, "dur": 23, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956932, "dur": 23, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956958, "dur": 26, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356956986, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957013, "dur": 29, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957043, "dur": 34, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957079, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957104, "dur": 22, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957128, "dur": 78, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957208, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957211, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957243, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957246, "dur": 28, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957277, "dur": 23, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957303, "dur": 82, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957388, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957413, "dur": 29, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957445, "dur": 34, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957480, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957482, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957506, "dur": 78, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957587, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957614, "dur": 30, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957647, "dur": 37, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957688, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957690, "dur": 20, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957713, "dur": 84, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957800, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957833, "dur": 39, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957875, "dur": 27, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957903, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957905, "dur": 26, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957933, "dur": 27, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957962, "dur": 26, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356957990, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958014, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958101, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958129, "dur": 39, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958170, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958199, "dur": 24, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958226, "dur": 85, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958312, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958345, "dur": 25, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958373, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958375, "dur": 27, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958405, "dur": 81, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958489, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958517, "dur": 28, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958548, "dur": 23, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958574, "dur": 77, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958653, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958683, "dur": 28, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958713, "dur": 23, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958739, "dur": 26, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958767, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958769, "dur": 29, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958800, "dur": 23, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958825, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958827, "dur": 38, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958867, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958892, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356958916, "dur": 87, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959005, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959037, "dur": 21, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959061, "dur": 23, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959086, "dur": 96, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959185, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959188, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959232, "dur": 2, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959235, "dur": 48, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959286, "dur": 1, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959288, "dur": 46, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959337, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959339, "dur": 32, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959373, "dur": 34, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959410, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959413, "dur": 94, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959512, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959555, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959556, "dur": 67, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959625, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959627, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959654, "dur": 80, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959740, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959776, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959777, "dur": 29, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959809, "dur": 82, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959894, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959924, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959926, "dur": 25, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356959954, "dur": 84, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960041, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960070, "dur": 27, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960099, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960122, "dur": 82, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960208, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960239, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960242, "dur": 28, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960273, "dur": 24, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960299, "dur": 78, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960381, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960416, "dur": 24, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960443, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960467, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960542, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960561, "dur": 26, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960590, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960608, "dur": 95, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960707, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960745, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960747, "dur": 39, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960789, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960791, "dur": 30, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960823, "dur": 94, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960922, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960958, "dur": 29, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356960989, "dur": 31, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961023, "dur": 76, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961101, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961133, "dur": 41, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961177, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961179, "dur": 24, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961205, "dur": 85, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961294, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961324, "dur": 27, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961354, "dur": 28, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961385, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961387, "dur": 68, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961459, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961494, "dur": 24, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961520, "dur": 25, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961547, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961572, "dur": 23, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961597, "dur": 94, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961695, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961724, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961725, "dur": 34, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961762, "dur": 25, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961790, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961792, "dur": 84, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961878, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961912, "dur": 27, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961943, "dur": 28, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356961973, "dur": 26, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962003, "dur": 83, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962088, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962118, "dur": 75, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962196, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962198, "dur": 24, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962224, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962305, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962343, "dur": 30, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962377, "dur": 26, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962405, "dur": 91, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962499, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962537, "dur": 24, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962563, "dur": 27, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962591, "dur": 2, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962593, "dur": 105, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962702, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962753, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962755, "dur": 37, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962795, "dur": 67, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962864, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962911, "dur": 28, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962940, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962941, "dur": 26, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356962970, "dur": 68, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963041, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963066, "dur": 26, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963095, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963097, "dur": 32, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963131, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963132, "dur": 24, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963158, "dur": 40, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963201, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963203, "dur": 31, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963236, "dur": 23, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963262, "dur": 82, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963346, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963377, "dur": 25, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963404, "dur": 88, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963494, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963519, "dur": 23, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963544, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963568, "dur": 24, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963595, "dur": 31, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963628, "dur": 23, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963654, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963656, "dur": 26, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963684, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963709, "dur": 92, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963804, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963851, "dur": 30, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963883, "dur": 28, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963913, "dur": 24, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963939, "dur": 24, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963966, "dur": 25, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356963993, "dur": 22, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964017, "dur": 29, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964050, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964079, "dur": 71, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964152, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964184, "dur": 27, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964214, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964240, "dur": 29, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964271, "dur": 22, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964295, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964297, "dur": 29, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964329, "dur": 21, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964353, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964378, "dur": 90, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964470, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964499, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964524, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964551, "dur": 30, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964583, "dur": 24, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964610, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964611, "dur": 29, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964643, "dur": 21, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964666, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964693, "dur": 95, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964791, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964814, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964852, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964877, "dur": 25, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964905, "dur": 31, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964939, "dur": 26, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964967, "dur": 20, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356964990, "dur": 23, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356965015, "dur": 24, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356965041, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356965065, "dur": 25, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356965092, "dur": 81, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356965175, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356965204, "dur": 136, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356965343, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356965345, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356965384, "dur": 1859, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967247, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967301, "dur": 7, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967309, "dur": 56, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967369, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967370, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967434, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967436, "dur": 43, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967483, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967486, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967536, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967538, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967584, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967587, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967638, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967641, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967693, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967695, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967737, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967779, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967781, "dur": 30, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967814, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967870, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967872, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967924, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967955, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967989, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356967990, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968025, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968122, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968125, "dur": 58, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968188, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968191, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968255, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968258, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968320, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968356, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968394, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968429, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968462, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968493, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968572, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968638, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968640, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968694, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968696, "dur": 33, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968732, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968798, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968800, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968853, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968856, "dur": 60, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968920, "dur": 7, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968929, "dur": 43, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968975, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356968978, "dur": 37, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356969018, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356969022, "dur": 39, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356969064, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356969067, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356969097, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356969098, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356969151, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356969153, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356969652, "dur": 66, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356969723, "dur": 805, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970530, "dur": 58, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970593, "dur": 8, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970602, "dur": 36, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970641, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970642, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970682, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970685, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970757, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970797, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970798, "dur": 32, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970833, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970863, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970892, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356970967, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971017, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971019, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971056, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971058, "dur": 48, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971110, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971113, "dur": 43, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971158, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971161, "dur": 41, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971208, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971210, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971252, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971254, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971307, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971308, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971343, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971383, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971385, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971435, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971438, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971485, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971488, "dur": 49, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971540, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971544, "dur": 57, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971604, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971606, "dur": 36, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971645, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971646, "dur": 167, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971818, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971874, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971878, "dur": 39, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971920, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971922, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971973, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356971976, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972005, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972048, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972051, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972092, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972095, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972135, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972138, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972171, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972173, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972212, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972215, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972258, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972261, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972299, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972302, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972357, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972360, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972420, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972422, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972475, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972479, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972519, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972522, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972570, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972572, "dur": 45, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972621, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972624, "dur": 32, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972659, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972661, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972696, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356972698, "dur": 4466, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356977174, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356977179, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356977242, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356977246, "dur": 3258, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356980513, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356980518, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356980582, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356980585, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356980641, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356980679, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356980682, "dur": 165, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356980852, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356980888, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356980890, "dur": 4387, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356985289, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356985295, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356985355, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356985357, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356985389, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356985393, "dur": 265, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356985662, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356985692, "dur": 341, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986037, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986065, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986099, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986101, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986303, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986340, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986343, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986405, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986407, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986444, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986446, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986476, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986507, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986509, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986537, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986539, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986582, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986584, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986615, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986819, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356986847, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987087, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987119, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987144, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987197, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987225, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987263, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987288, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987376, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987403, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987429, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987455, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987526, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987550, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987588, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987616, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987618, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987651, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987680, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987714, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987717, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987743, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987775, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987804, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987833, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987861, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987892, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356987979, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988007, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988032, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988061, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988109, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988138, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988165, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988195, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988197, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988222, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988248, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988250, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988283, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988314, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988316, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988344, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988368, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988370, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988447, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988450, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988481, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988482, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988510, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988544, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988575, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988577, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988607, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988634, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988670, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988697, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988812, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988854, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988856, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356988891, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989023, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989025, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989074, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989076, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989111, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989114, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989145, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989147, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989179, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989360, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989392, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989395, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989424, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989460, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989488, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989490, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989527, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989559, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989561, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989593, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989633, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989659, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989725, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989754, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989757, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989785, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989848, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989891, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989893, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356989968, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990009, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990011, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990055, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990057, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990094, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990098, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990125, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990190, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990227, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990229, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990264, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990265, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990302, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990304, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990350, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990352, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990388, "dur": 275, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990669, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990698, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990725, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990763, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990765, "dur": 28, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990798, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990917, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990920, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990959, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356990961, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356991026, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356991058, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356991061, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356991199, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356991201, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356991247, "dur": 1461, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356992713, "dur": 67, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356992782, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356992785, "dur": 37, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356992824, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356992826, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356992863, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993041, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993044, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993085, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993087, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993116, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993117, "dur": 456, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993577, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993622, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993624, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993663, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993664, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993780, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993827, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993831, "dur": 138, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356993975, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994009, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994040, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994118, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994155, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994156, "dur": 216, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994375, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994406, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994510, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994534, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994565, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994672, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994701, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994781, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994806, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994837, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994947, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994982, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356994984, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995015, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995017, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995057, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995245, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995278, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995303, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995349, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995384, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995386, "dur": 297, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995687, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356995716, "dur": 758, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356996477, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356996507, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280356996512, "dur": 89863, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357086383, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357086388, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357086419, "dur": 5093, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357091520, "dur": 4189, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357095718, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357095722, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357095760, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357095762, "dur": 187, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357095952, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357095955, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357095988, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357096048, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357096080, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357096115, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357096146, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357096189, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357096227, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357096229, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357096263, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357096265, "dur": 243, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357096514, "dur": 1331, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357097849, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357097884, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357097975, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098009, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098180, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098211, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098213, "dur": 323, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098539, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098543, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098574, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098600, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098661, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098686, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098780, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098810, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098812, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098841, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098843, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098885, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357098921, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357099053, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357099083, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357099399, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357099438, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357099440, "dur": 549, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357099994, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357100027, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357100029, "dur": 616, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357100648, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357100676, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357100933, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357100967, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357100969, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357101094, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357101129, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357101158, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357101160, "dur": 539, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357101703, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357101733, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357101794, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357101822, "dur": 302, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102128, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102158, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102160, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102188, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102380, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102413, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102443, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102497, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102529, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102703, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102737, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357102738, "dur": 639, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357103380, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357103408, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357103410, "dur": 225, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357103638, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357103701, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357103703, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357103787, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357103816, "dur": 549, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357104369, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357104399, "dur": 404, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357104808, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357104835, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357104898, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357104928, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357104931, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357104973, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105003, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105032, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105073, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105104, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105138, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105169, "dur": 40, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105212, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105214, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105240, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105274, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105310, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105311, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105339, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105341, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105365, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105366, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105392, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105394, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105418, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105444, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105484, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105486, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105537, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105540, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105575, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105577, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105619, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105621, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105659, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105661, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105695, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105698, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105739, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105741, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105775, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105804, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105806, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105837, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105839, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105878, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105881, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105910, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105912, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105941, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105968, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105970, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105997, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357105998, "dur": 26, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106027, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106029, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106060, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106062, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106092, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106094, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106127, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106129, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106163, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106165, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106215, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106217, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106275, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106278, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106321, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106323, "dur": 70, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106396, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106398, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106467, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106494, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106518, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106553, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106577, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106654, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106697, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106723, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357106725, "dur": 311, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357107041, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357107083, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357107085, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357107120, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357107121, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357107152, "dur": 288035, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357395197, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357395201, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357395247, "dur": 20, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357395269, "dur": 19339, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357414618, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357414623, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357414683, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357414685, "dur": 72961, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357487656, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357487661, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357487715, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357487719, "dur": 611, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357488335, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357488373, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357488376, "dur": 132494, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357620881, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357620887, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357620935, "dur": 25, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357620962, "dur": 4067, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357625037, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357625042, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357625096, "dur": 54, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357625152, "dur": 17829, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357642994, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357643000, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357643063, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357643067, "dur": 1431, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357644506, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357644510, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357644562, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357644565, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357644736, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357644781, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357644806, "dur": 29199, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357674014, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357674019, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357674074, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357674076, "dur": 44521, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357718610, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357718618, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357718668, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357718674, "dur": 806, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357719488, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357719493, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357719553, "dur": 31, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357719586, "dur": 661, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357720251, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357720253, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357720292, "dur": 377, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 1472, "tid": 12884901888, "ts": 1753280357720672, "dur": 9697, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 1472, "tid": 4277, "ts": 1753280357772754, "dur": 1672, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 1472, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 1472, "tid": 8589934592, "ts": 1753280356915492, "dur": 187766, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 1472, "tid": 8589934592, "ts": 1753280357103262, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 1472, "tid": 8589934592, "ts": 1753280357103269, "dur": 3590, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 1472, "tid": 4277, "ts": 1753280357774428, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 1472, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 1472, "tid": 4294967296, "ts": 1753280356837916, "dur": 893852, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 1472, "tid": 4294967296, "ts": 1753280356852632, "dur": 23159, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 1472, "tid": 4294967296, "ts": 1753280357732373, "dur": 13129, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 1472, "tid": 4294967296, "ts": 1753280357739659, "dur": 377, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 1472, "tid": 4294967296, "ts": 1753280357745616, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 1472, "tid": 4277, "ts": 1753280357774435, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753280356917160, "dur": 51, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753280356917241, "dur": 2460, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753280356919715, "dur": 1139, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753280356921036, "dur": 90, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753280356921126, "dur": 745, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753280356923235, "dur": 7569, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753280356932012, "dur": 5753, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_22FDE6BD161A4064.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753280356942762, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753280356943067, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753280356943213, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753280356943926, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753280356946936, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753280356947045, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_9468B054363B0720.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753280356950093, "dur": 3110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753280356959428, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753280356961998, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753280356921897, "dur": 43156, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753280356965070, "dur": 754368, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753280357719440, "dur": 355, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753280357719993, "dur": 87, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753280357720110, "dur": 1392, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753280356921994, "dur": 43091, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356965115, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356965216, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_8670CD93B0BDDB1D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753280356965383, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356965854, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356966353, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356967030, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356967255, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7AD711F5F88D031D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753280356967420, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_B5974478C0B40EC1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753280356967477, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356968009, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356968295, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356968570, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356968693, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356968826, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356968988, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356969184, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356969708, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356969848, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356970066, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356970459, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356971197, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356971823, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356971984, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753280356972208, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356972430, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356972840, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356973648, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356974168, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356974389, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356975675, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356976170, "dur": 693, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Nodes\\Utility\\Logic\\ComparisonNode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753280356976169, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356977462, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356978045, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356978610, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356978831, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356979447, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356979752, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356979972, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356980665, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356980895, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356981194, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356981793, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356982636, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356983269, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356984037, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356984625, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356985173, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356985980, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753280356986164, "dur": 2189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753280356988354, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356988505, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356988687, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753280356989000, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753280356990463, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356990562, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753280356990772, "dur": 1144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753280356991985, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753280356992098, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753280356992447, "dur": 2503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280356994950, "dur": 98130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280357093082, "dur": 6141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753280357099273, "dur": 2776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753280357102051, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280357102359, "dur": 2780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753280357105140, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280357105975, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280357106169, "dur": 538026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753280357644221, "dur": 29535, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753280357644197, "dur": 29562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753280357673819, "dur": 45617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356922048, "dur": 43067, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356965123, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1FDEA3861C839C9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753280356965244, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356965553, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356965667, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_68DB32F33070957F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753280356965749, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356965964, "dur": 844, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356966847, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356967228, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_36273396CDE3C103.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753280356967337, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_ABDE5DF73D4F51D2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753280356967388, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356967607, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_A38368206D763C77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753280356967658, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356967761, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_A38368206D763C77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753280356967881, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356968206, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356968547, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356968684, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356968862, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753280356968919, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356969084, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356969426, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356969616, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753280356969745, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356969909, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356970154, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356970460, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356971151, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356971709, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753280356971830, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356972037, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356972215, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356972434, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356973105, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356973806, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356974889, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356975531, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356976248, "dur": 575, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Inspector\\MasterPreviewView.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753280356975966, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356977265, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356977689, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356977906, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356978562, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356979329, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356979772, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356980008, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356980589, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356981041, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356981827, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356982640, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356983282, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356983914, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356984632, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356985178, "dur": 984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356986169, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753280356986391, "dur": 667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356987076, "dur": 927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753280356988146, "dur": 1058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753280356989205, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356989345, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356989497, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753280356989686, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356990007, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753280356990112, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753280356990685, "dur": 4260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280356994946, "dur": 98103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280357093051, "dur": 2840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753280357095892, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280357096066, "dur": 2550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753280357098617, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280357098790, "dur": 3100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753280357101891, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753280357102252, "dur": 3952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753280357106322, "dur": 613108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356922039, "dur": 43064, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356965117, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356965383, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_3397DEB3C6437C6C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753280356965451, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356965652, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_5E49D6BEFCDFF46D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753280356965713, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356965869, "dur": 734, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356966643, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356967216, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_06983A7F9FE1EEBF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753280356967410, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_8548D09514643BAC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753280356967474, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356967718, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356968016, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356968342, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753280356968420, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356968602, "dur": 8321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753280356977080, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356977758, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356978693, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356978906, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356979229, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356979484, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356979884, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356980095, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356981336, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356981581, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356982228, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356982798, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356983387, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356983847, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356984590, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356985191, "dur": 849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356986050, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753280356986223, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356986377, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753280356987352, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356987520, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753280356988410, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356988705, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356988978, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753280356989231, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753280356989839, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356989945, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1753280356990614, "dur": 108, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280356991072, "dur": 95154, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1753280357093056, "dur": 2496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753280357095596, "dur": 2340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753280357097937, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280357098052, "dur": 2268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753280357100321, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280357100516, "dur": 2688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753280357103248, "dur": 2740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753280357106111, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753280357106423, "dur": 613175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356922094, "dur": 43034, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356965137, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_52C906CE0C7BEE08.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356965249, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356965620, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7C852BDA8365271E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356965720, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356965858, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_88FDBEF459069934.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356965914, "dur": 831, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356966783, "dur": 444, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356967228, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_CB49B96AA053BB61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356967310, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_31DA0AEF6717D1CD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356967429, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356967630, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356967905, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_5D725690D6CA2AA1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356968080, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356968354, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356968528, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356968647, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356968753, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356968876, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356969002, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753280356969198, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356969632, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356969830, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356970072, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356970485, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356970648, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356971017, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356971407, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356971943, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753280356972044, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356972252, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356972443, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356973083, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356973799, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356974017, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356974684, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356975326, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356976252, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Inspector\\PropertyDrawers\\SampleTexture2DNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753280356975876, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356977134, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356977712, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356977927, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356978690, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356978916, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356979194, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356979535, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356979823, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356980059, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356980731, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356981191, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356981719, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356982367, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356982896, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356983450, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356984000, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356984641, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356985180, "dur": 904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356986092, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356986316, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356986444, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356986598, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356987065, "dur": 1284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753280356988350, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356988447, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356988689, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356988943, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753280356989565, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356989629, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356989720, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356989955, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356990651, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356990830, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356991094, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753280356991683, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356991871, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753280356992719, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356992897, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753280356993487, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753280356993663, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753280356994245, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280356994934, "dur": 98172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280357093107, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753280357095481, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280357095603, "dur": 2201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753280357097847, "dur": 3059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753280357100967, "dur": 2984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753280357103953, "dur": 806, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280357104772, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280357104938, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280357105085, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280357105525, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280357105695, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280357106162, "dur": 536451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753280357642637, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1753280357642614, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1753280357642804, "dur": 1803, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1753280357644610, "dur": 74840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356922133, "dur": 43008, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356965153, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_13ED6B36C70FAB0A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356965256, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356965722, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_1A11F72A8792752A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356965784, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356966048, "dur": 850, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356966938, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356967094, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_DE30F0D9293C6825.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356967267, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_672767AC8B5C81E9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356967415, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356967605, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0B3C92E79B34D00C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356967667, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356967768, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0B3C92E79B34D00C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356967900, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356968221, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356968539, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356968672, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356968812, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356968946, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356969113, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356969491, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356969574, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753280356969646, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356969955, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356970202, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356970470, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356970623, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356971017, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356971357, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356971761, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356971970, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356972198, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356972340, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356972447, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356973123, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356973738, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356974059, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356974549, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356975074, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356976249, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Views\\Slots\\TextureSlotControlView.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753280356975723, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356976977, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356977575, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356978120, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356978784, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356979009, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356979307, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356979565, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356979828, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356980075, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356980735, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356981064, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356982305, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356982915, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356983525, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356983718, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356983817, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356984585, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356985169, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356985978, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356986095, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356986293, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753280356987000, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356987320, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356987548, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753280356988478, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356988542, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356988703, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356988931, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753280356989548, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356989728, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356989950, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356990651, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356990801, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753280356991339, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356991458, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356991658, "dur": 3262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280356994922, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753280356995159, "dur": 97906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280357093067, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753280357095477, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280357095568, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753280357097939, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280357098446, "dur": 3877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753280357102324, "dur": 1319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753280357103653, "dur": 2701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753280357106430, "dur": 613157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356922191, "dur": 42964, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356965166, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_54FADE420F1D3CF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356965259, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356965561, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356965718, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_42CC435405CEA14C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356965781, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356965995, "dur": 910, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356966959, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356967264, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_15E32D3DB1F12CD0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356967430, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356967532, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2039BDFA7AABC76E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356967603, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356967908, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2039BDFA7AABC76E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356968014, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356968225, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356968521, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753280356968620, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753280356968679, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356968891, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753280356968944, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356969115, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356969502, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356969675, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356969869, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356970122, "dur": 623, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356970756, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356971437, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356971746, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356971956, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356972068, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356972331, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356972461, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356972811, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356973579, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356973850, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356974521, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356975195, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356976224, "dur": 561, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Inspector\\PropertyDrawerUtils.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753280356975788, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356977049, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356977669, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356977896, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356978653, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356978880, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356979365, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356979609, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356979921, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356980154, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356980914, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356981442, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356982207, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356982853, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356983378, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356984028, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356984696, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356985195, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356986010, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356986147, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356986302, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356986435, "dur": 943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753280356987379, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356987711, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356987976, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753280356988817, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356988982, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356989220, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753280356989835, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356989957, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356990652, "dur": 1882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356992535, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356992662, "dur": 1139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753280356993802, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280356993881, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356994008, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753280356994679, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753280356994806, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753280356995214, "dur": 97844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280357093061, "dur": 2658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753280357095720, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280357095790, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753280357096075, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753280357098462, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280357098531, "dur": 2343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753280357100875, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280357101009, "dur": 2751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753280357103761, "dur": 902, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753280357104672, "dur": 2205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753280357106981, "dur": 612527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356922223, "dur": 42948, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356965182, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_2972E28E15539B90.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753280356965267, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356965659, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B7E1353FAF528495.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753280356965735, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356965828, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_AFAA4BAD90F58029.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753280356965891, "dur": 764, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356966690, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356967211, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_B522ABDF46A7E881.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753280356967417, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_64D3851878536F69.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753280356967478, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356967825, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356968121, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356968491, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356968763, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_5A43EC8C14EFD6DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753280356968914, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356969096, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356969387, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356969618, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356969812, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356970059, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356970384, "dur": 588, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356971007, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356971397, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356971748, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356972025, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356972217, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356972401, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356972616, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356973328, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356974154, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356974381, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356976247, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Views\\GradientEdge.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753280356975787, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356977399, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356978196, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356978779, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356978989, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356979261, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356979521, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356980231, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356980503, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356980746, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356981031, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356981881, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356982461, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356982970, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356983867, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356984679, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356985184, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356986074, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753280356986262, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753280356986501, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356986688, "dur": 917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753280356987606, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356987689, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753280356987998, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356988164, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753280356989169, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356989335, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356989435, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356989950, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356990647, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753280356990815, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356991064, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753280356991661, "dur": 3286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280356994948, "dur": 98128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280357093077, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753280357095556, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280357095843, "dur": 4718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753280357100562, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753280357100800, "dur": 2666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753280357103509, "dur": 2968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753280357106551, "dur": 613040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356922259, "dur": 42929, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356965199, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EA7E59FB55CB0915.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753280356965280, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356965556, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356965815, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_98C9D7CFA3055094.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753280356965876, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356966584, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356967204, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3DE1FE7E9BAAF7A9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753280356967263, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356967462, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_CA1900490E2D89BF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753280356967514, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356967910, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_CA1900490E2D89BF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753280356967996, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356968211, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356968530, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356968662, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356968824, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753280356968932, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356969019, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356969197, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356969625, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356969815, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356970082, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356970642, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356970931, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753280356971084, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356971478, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356971789, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356972015, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356972170, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356972364, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356972522, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356973415, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356974097, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356974314, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356974874, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356976237, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Generation\\TargetResources\\FieldDependencies.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753280356975518, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356976962, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356977546, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356978058, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356978604, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356979169, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356979402, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356979726, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356979964, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356980205, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356980470, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356980763, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356981015, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356981405, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356982088, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356982671, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356983156, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356984108, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356984729, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356985165, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356985982, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753280356986133, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356986346, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753280356987212, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356987463, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356987561, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753280356987848, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356987921, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753280356988855, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356989035, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753280356989242, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753280356989985, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753280356990149, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356990645, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753280356990835, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356991129, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753280356991655, "dur": 3309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280356994965, "dur": 98122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280357093103, "dur": 2481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753280357095625, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753280357095832, "dur": 2733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753280357098566, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280357098698, "dur": 3294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753280357102031, "dur": 2748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753280357104884, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280357105029, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280357105325, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280357105447, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280357105613, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280357105713, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.unity.cinemachine.editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1753280357105897, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280357106112, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753280357106557, "dur": 613010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356922287, "dur": 43103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356965391, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_DDBE5B8EC557ADE5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753280356965459, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356965728, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_94383F50AE2F3B27.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753280356965907, "dur": 761, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356966705, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356967248, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_6E9570E2A3C5474A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753280356967489, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356967811, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356968080, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356968382, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356968539, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356968663, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356968796, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356968854, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753280356968944, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356969105, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356969366, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356969478, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356969603, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356969668, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753280356969757, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356970103, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356970775, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356971375, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356971693, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753280356971824, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356971960, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356972236, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356972296, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753280356972369, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356972621, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356973300, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356973960, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356974880, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356975451, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356976236, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Colors\\ColorManager.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753280356976024, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356977377, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356977996, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356978629, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356978859, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356979507, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356979770, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356979995, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356980863, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356981429, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356982085, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356982866, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356983463, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356984010, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356984663, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356985194, "dur": 821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356986024, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753280356986203, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753280356987282, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356987394, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753280356987666, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356987773, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356988013, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753280356988316, "dur": 1397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753280356989713, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356989879, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356989975, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753280356990056, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356990673, "dur": 4269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280356994943, "dur": 98129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280357093082, "dur": 2955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753280357096038, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280357096094, "dur": 2343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Animation.Rigging.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753280357098438, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280357098649, "dur": 3591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753280357102241, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280357102599, "dur": 2864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753280357105464, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280357105984, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280357106160, "dur": 293070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280357399255, "dur": 85671, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753280357399233, "dur": 87283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753280357487998, "dur": 183, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753280357488466, "dur": 132232, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753280357642624, "dur": 75732, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753280357642608, "dur": 75751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753280357718390, "dur": 960, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753280356922313, "dur": 42910, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356965235, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_30E5849A6622DFB0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753280356965305, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356965740, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E7EDEE91A237FB4D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753280356965793, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356966219, "dur": 755, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356967024, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356967241, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B95C3234EB0BE89D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753280356967324, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4D1C445C9CC0E084.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753280356967474, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356967654, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356968008, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356968249, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356968537, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753280356968635, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356968961, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356969123, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356969369, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356969467, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356969614, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753280356969672, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356969844, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356969990, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356970141, "dur": 679, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356970835, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356971153, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356971412, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356971772, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356971926, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753280356972064, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356972415, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753280356972499, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356973547, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356974082, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356975015, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\ClipInspector\\ClipInspectorSelectionInfo.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753280356974487, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356976239, "dur": 564, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Views\\PreviewSceneResources.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753280356975772, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356976933, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356977485, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356977908, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356978425, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356979488, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356979808, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356980309, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356980599, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356981035, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356981633, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356982286, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356982981, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356983473, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356983812, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356984680, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356985187, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356986061, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753280356986217, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356986287, "dur": 2569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753280356988857, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356988966, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356989416, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753280356989627, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753280356990169, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356990661, "dur": 4264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280356994926, "dur": 98128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280357093082, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753280357095554, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280357095917, "dur": 2740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753280357098703, "dur": 3418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753280357102122, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280357102300, "dur": 3380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753280357105758, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280357105920, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280357106102, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753280357106329, "dur": 613098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356922341, "dur": 42905, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356965260, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_4D5EF4ECE256D09B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356965333, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356965574, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356965768, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C49902DAED8AF95B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356965832, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356966223, "dur": 737, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356967007, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356967227, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8F85FFFDEEDD670C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356967342, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_53CC86BB524A4E38.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356967398, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356967596, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_36EDDF0496264E2B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356967655, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356967741, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_36EDDF0496264E2B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356967811, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356968115, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356968427, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356968589, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356968718, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356968878, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356969030, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753280356969177, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356969506, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356969641, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356969886, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356970103, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356970322, "dur": 602, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356970961, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356971252, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356971454, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356971841, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356971966, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753280356972018, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356972177, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356972331, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356972457, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356973007, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356973799, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356974294, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356974512, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356975433, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356976212, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Controls\\EnumControl.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753280356976001, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356977234, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356978132, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356978726, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356978950, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356979236, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356979563, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356979830, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356980129, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356980738, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356981092, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356981828, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356982403, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356983005, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356983442, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356984271, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356984562, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356985161, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356985984, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356986142, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356986350, "dur": 1182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753280356987533, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356987643, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356987927, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356988048, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753280356988828, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356988935, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356989018, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356989944, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356990570, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356990738, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356990807, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753280356992348, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356992445, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356992609, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753280356993485, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356993627, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280356993836, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753280356994412, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356994541, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753280356994947, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753280356995107, "dur": 97989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280357093097, "dur": 2895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753280357095999, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280357096167, "dur": 3058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753280357099268, "dur": 3878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753280357103148, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280357103254, "dur": 2747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Animation.Rigging.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753280357106003, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280357106156, "dur": 21853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753280357128010, "dur": 591465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356922379, "dur": 42887, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356965278, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8E3C9D84E1638511.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356965422, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356965634, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_F66768BDA37A5632.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356965729, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356965996, "dur": 903, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356966941, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356967184, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_F7B8A20762668D38.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356967239, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356967428, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356967601, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_EF67977AD200318E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356967653, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356967746, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_EF67977AD200318E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356967828, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356968193, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356968313, "dur": 11974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753280356980423, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356980712, "dur": 4356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753280356985236, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356985339, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356985523, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753280356985983, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356986166, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753280356986917, "dur": 607, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356987610, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356987876, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753280356988653, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356988975, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356989238, "dur": 1270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753280356990509, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356990656, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356990887, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753280356991398, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356991465, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356991653, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356991864, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753280356992525, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753280356992633, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753280356992958, "dur": 1994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280356994952, "dur": 98140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280357093093, "dur": 2614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753280357095753, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753280357095845, "dur": 3434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753280357099280, "dur": 569, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280357099876, "dur": 2636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753280357102513, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280357102574, "dur": 2715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753280357105290, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280357105423, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280357105944, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280357106013, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280357106089, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1753280357106143, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753280357106989, "dur": 612497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356922410, "dur": 42880, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356965304, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_326BFA302771D2EF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753280356965386, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356965803, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7CFFD4AD9CA821AB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753280356965856, "dur": 617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356966514, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356967042, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356967257, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_17C8569D27D04AA9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753280356967383, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356967494, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356967905, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0F4B26DD7F6C67C4.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753280356968096, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356968351, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356968533, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356968744, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356968873, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753280356968926, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356969071, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753280356969199, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356969575, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356969686, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356969862, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356970032, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753280356970135, "dur": 776, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356970940, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356971145, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356971752, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356971913, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753280356971966, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356972073, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356972290, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356972454, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356973038, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356973761, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356973965, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356974501, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356975224, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356976237, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Util\\MipSamplingModes.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753280356976049, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356977313, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356977754, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356978211, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356978986, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356979269, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356979497, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356979797, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356980025, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356980777, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356981048, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356981682, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356982308, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356982990, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356983947, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356984585, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356985193, "dur": 835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356986037, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753280356986194, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356986334, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753280356987085, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356987574, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753280356987846, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753280356988668, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356988744, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356988940, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356989043, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.DocCodeExamples.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753280356989265, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356989371, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.DocCodeExamples.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753280356989923, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356989977, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.DocCodeExamples.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753280356990080, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356990657, "dur": 4028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280356994686, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753280356994839, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753280356995217, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753280356995555, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753280356997321, "dur": 397714, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753280357399590, "dur": 14660, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753280357399224, "dur": 15127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753280357414353, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280357415041, "dur": 69874, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753280357414419, "dur": 71436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753280357487287, "dur": 179, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753280357487880, "dur": 136996, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753280357644200, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1753280357644191, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1753280357644336, "dur": 75125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356922440, "dur": 42873, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356965333, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B4FC9B590A58509F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753280356965409, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356965624, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_12B7B81D94FFC9B7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753280356965799, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_10ADE42C79389F20.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753280356965861, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356966436, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356967044, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356967297, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_95CE26B7D8385BCA.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753280356967444, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356967707, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0EB311F2080B4776.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753280356968099, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356968364, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356968517, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753280356968579, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356968724, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753280356968827, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753280356968887, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356969018, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356969182, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356969569, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356969696, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356970041, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356970151, "dur": 754, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356970989, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356971250, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356971463, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356971835, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356972029, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356972178, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356972297, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753280356972444, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356973956, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.43f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Features.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753280356973054, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356974554, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356975088, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356976250, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Nodes\\UV\\RadialShearNode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753280356976135, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356977337, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356978089, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356978807, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356979421, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356979765, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356979984, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356980715, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356981086, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356981636, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356982155, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356983095, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356983801, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356984583, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356985160, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356985986, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753280356986124, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356986282, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753280356986992, "dur": 950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753280356987943, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356988229, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356988431, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356988705, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753280356989015, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753280356989700, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356989849, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356989981, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1753280356990094, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356990655, "dur": 2836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356993492, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753280356993638, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753280356994004, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280356994953, "dur": 98331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280357093286, "dur": 2626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Animation.Rigging.DocCodeExamples.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753280357095913, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280357095984, "dur": 2383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753280357098408, "dur": 2642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753280357101051, "dur": 598, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753280357101660, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753280357104236, "dur": 2603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753280357106943, "dur": 612607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356922476, "dur": 42866, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356965354, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BEBB047E8EA0A5FC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753280356965429, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356965629, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_7FFEF131F3AD3BA4.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753280356965773, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_84BF5D8C55DA5D3D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753280356965829, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356966222, "dur": 762, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356967032, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356967395, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_0EF0635D76D99CD0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753280356967446, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356967671, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356968020, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753280356968076, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753280356968128, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356968511, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356968719, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356968936, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356969055, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753280356969131, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356969569, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753280356969750, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356970013, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356970087, "dur": 581, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356970675, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356970982, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356971146, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356971366, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356971704, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753280356971754, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356972063, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356972380, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356972484, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356972994, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356973729, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356974193, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356974400, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356975600, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356976240, "dur": 574, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Nodes\\Procedural\\CheckerboardNode.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753280356976171, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356977421, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356977908, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356978509, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356979199, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356979418, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356979875, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356980089, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356980944, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356981277, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356981554, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356982066, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356982641, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356983217, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356983822, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356984419, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356984479, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356984583, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356985204, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356985999, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753280356986182, "dur": 941, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356987134, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753280356987941, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356988070, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753280356988309, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356988364, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753280356989110, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356989331, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753280356989590, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753280356990210, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356990666, "dur": 4257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280356994925, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753280356995127, "dur": 99266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280357094395, "dur": 3261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753280357097712, "dur": 3660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753280357101374, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280357101570, "dur": 2770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753280357104342, "dur": 744, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280357105098, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280357105403, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280357105533, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280357105714, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1753280357105906, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280357105997, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280357106138, "dur": 767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753280357106947, "dur": 612581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356922518, "dur": 42842, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356965418, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356965647, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_5E20C3D8D90E43EC.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753280356965709, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356965841, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_774CD8E5A270CA86.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753280356965920, "dur": 891, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356966851, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356967247, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_3359014152B0440F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753280356967376, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356967520, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356967979, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356968162, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356968487, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356968681, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753280356968746, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356968910, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356969050, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356969173, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356969687, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356969839, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356970090, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356970533, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356970986, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356971116, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356971496, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356971764, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356972110, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356972451, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356973057, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356973807, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356974568, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356976242, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Serialization\\JsonData.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753280356975411, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356976809, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356977496, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356977966, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356978594, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356978925, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356979235, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356979470, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356979760, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356979991, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356980500, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356980875, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356981186, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356981886, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356982505, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356983151, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356983909, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356984633, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356985177, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356986183, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753280356986340, "dur": 718, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356987081, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753280356987638, "dur": 784, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356988493, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356988706, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356988989, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753280356989329, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356989615, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753280356990205, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356990670, "dur": 4267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280356994937, "dur": 98115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357093053, "dur": 2826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753280357095932, "dur": 2933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753280357098920, "dur": 2560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753280357101481, "dur": 505, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357102012, "dur": 2647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753280357104660, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357105044, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357105148, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357105217, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357105379, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753280357105452, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357105572, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357105972, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357106117, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357108218, "dur": 1076, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 16, "ts": 1753280357109294, "dur": 18593, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 16, "ts": 1753280357127888, "dur": 109, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 16, "ts": 1753280357106733, "dur": 21271, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753280357128004, "dur": 591491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753280357727928, "dur": 1773, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 1472, "tid": 4277, "ts": 1753280357775261, "dur": 7926, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 1472, "tid": 4277, "ts": 1753280357783259, "dur": 5250, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 1472, "tid": 4277, "ts": 1753280357767405, "dur": 24314, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}