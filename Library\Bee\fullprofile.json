{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 24492, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 24492, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 24492, "tid": 637, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 24492, "tid": 637, "ts": 1754114546564686, "dur": 1650, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 24492, "tid": 637, "ts": 1754114546574731, "dur": 1518, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 24492, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 24492, "tid": 1, "ts": 1754114545407987, "dur": 9486, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 24492, "tid": 1, "ts": 1754114545417478, "dur": 140432, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 24492, "tid": 1, "ts": 1754114545557929, "dur": 270117, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 24492, "tid": 637, "ts": 1754114546576257, "dur": 26, "ph": "X", "name": "", "args": {}}, {"pid": 24492, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545404730, "dur": 22911, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545427647, "dur": 1122396, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545429028, "dur": 4339, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545433377, "dur": 2723, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436108, "dur": 370, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436481, "dur": 12, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436495, "dur": 62, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436560, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436561, "dur": 49, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436612, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436614, "dur": 32, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436647, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436649, "dur": 45, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436698, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436705, "dur": 115, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436826, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436874, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436877, "dur": 44, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436922, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436925, "dur": 37, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436964, "dur": 29, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436996, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545436997, "dur": 31, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437031, "dur": 35, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437070, "dur": 32, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437105, "dur": 33, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437143, "dur": 150, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437295, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437298, "dur": 203, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437504, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437507, "dur": 42, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437551, "dur": 32, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437586, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437589, "dur": 53, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437644, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437646, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437689, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437692, "dur": 38, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437733, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437735, "dur": 204, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437942, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545437944, "dur": 63, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438010, "dur": 3, "ph": "X", "name": "ProcessMessages 2464", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438015, "dur": 35, "ph": "X", "name": "ReadAsync 2464", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438052, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438054, "dur": 33, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438089, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438092, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438120, "dur": 28, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438155, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438157, "dur": 27, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438187, "dur": 41, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438231, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438234, "dur": 40, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438276, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438279, "dur": 32, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438343, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438345, "dur": 50, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438399, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438402, "dur": 114, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438519, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438527, "dur": 39, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438569, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438572, "dur": 43, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438618, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438622, "dur": 61, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438685, "dur": 2, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438688, "dur": 46, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438738, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438741, "dur": 16, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438760, "dur": 10, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438772, "dur": 14, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438789, "dur": 14, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438806, "dur": 27, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438835, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438837, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545438974, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439038, "dur": 2, "ph": "X", "name": "ProcessMessages 1101", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439041, "dur": 37, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439080, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439082, "dur": 68, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439152, "dur": 1, "ph": "X", "name": "ProcessMessages 133", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439153, "dur": 42, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439199, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439202, "dur": 249, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439455, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439457, "dur": 40, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439499, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439501, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439555, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439604, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439606, "dur": 87, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439699, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439702, "dur": 55, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439761, "dur": 2, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439764, "dur": 45, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439811, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439813, "dur": 93, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439911, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439961, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545439964, "dur": 238, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440205, "dur": 3, "ph": "X", "name": "ProcessMessages 2082", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440209, "dur": 47, "ph": "X", "name": "ReadAsync 2082", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440260, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440263, "dur": 54, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440320, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440322, "dur": 46, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440371, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440375, "dur": 45, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440424, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440426, "dur": 54, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440484, "dur": 2, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440487, "dur": 37, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440529, "dur": 57, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440591, "dur": 2, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440594, "dur": 61, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440659, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440662, "dur": 49, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440714, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440717, "dur": 61, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440781, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440784, "dur": 73, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440860, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440862, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440910, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440913, "dur": 63, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440979, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545440982, "dur": 40, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441024, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441025, "dur": 54, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441083, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441086, "dur": 83, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441172, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441175, "dur": 47, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441225, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441228, "dur": 67, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441299, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441302, "dur": 57, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441361, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441364, "dur": 47, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441413, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441415, "dur": 44, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441462, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441465, "dur": 36, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441504, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441506, "dur": 46, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441555, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441558, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441617, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441620, "dur": 48, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441670, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441673, "dur": 44, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441719, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441720, "dur": 38, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441761, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441764, "dur": 50, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441819, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441863, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441866, "dur": 42, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441910, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441912, "dur": 53, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441968, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545441970, "dur": 41, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442020, "dur": 38, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442060, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442062, "dur": 30, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442094, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442096, "dur": 34, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442133, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442193, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442196, "dur": 46, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442245, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442247, "dur": 63, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442314, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442315, "dur": 40, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442358, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442360, "dur": 36, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442400, "dur": 48, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442453, "dur": 362, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442819, "dur": 2, "ph": "X", "name": "ProcessMessages 2241", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442822, "dur": 36, "ph": "X", "name": "ReadAsync 2241", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442862, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442865, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442903, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442905, "dur": 36, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442944, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442947, "dur": 42, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442990, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545442992, "dur": 43, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443038, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443041, "dur": 37, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443080, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443082, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443108, "dur": 34, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443145, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443146, "dur": 34, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443182, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443184, "dur": 137, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443325, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443327, "dur": 48, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443379, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443381, "dur": 36, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443420, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443421, "dur": 70, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443495, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443498, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443546, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443548, "dur": 39, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443589, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443590, "dur": 41, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443633, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443635, "dur": 36, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443675, "dur": 32, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443710, "dur": 35, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443749, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443751, "dur": 42, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443797, "dur": 28, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443831, "dur": 31, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443865, "dur": 35, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443903, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443905, "dur": 46, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443953, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443955, "dur": 40, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545443999, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444000, "dur": 35, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444038, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444040, "dur": 37, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444080, "dur": 62, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444145, "dur": 2, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444148, "dur": 41, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444192, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444194, "dur": 51, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444248, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444250, "dur": 33, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444288, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444332, "dur": 33, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444367, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444369, "dur": 88, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444460, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444462, "dur": 45, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444510, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444512, "dur": 46, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444562, "dur": 44, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444610, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444612, "dur": 47, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444662, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444664, "dur": 42, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444708, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444714, "dur": 41, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444758, "dur": 1, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444760, "dur": 41, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444804, "dur": 33, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444839, "dur": 25, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444867, "dur": 26, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444895, "dur": 40, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444937, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444965, "dur": 23, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545444990, "dur": 23, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445016, "dur": 23, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445041, "dur": 31, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445075, "dur": 27, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445104, "dur": 21, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445127, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445129, "dur": 35, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445167, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445195, "dur": 29, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445226, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445228, "dur": 28, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445259, "dur": 46, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445307, "dur": 33, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445343, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445372, "dur": 42, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445417, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445419, "dur": 50, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445474, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445517, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445519, "dur": 52, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445574, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445576, "dur": 40, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445619, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445622, "dur": 41, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445665, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445667, "dur": 86, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445756, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445758, "dur": 37, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445797, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445799, "dur": 47, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445849, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445851, "dur": 36, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545445889, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446068, "dur": 72, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446143, "dur": 3, "ph": "X", "name": "ProcessMessages 3137", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446147, "dur": 40, "ph": "X", "name": "ReadAsync 3137", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446190, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446195, "dur": 46, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446243, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446246, "dur": 34, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446282, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446283, "dur": 32, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446317, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446344, "dur": 24, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446371, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446401, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446437, "dur": 34, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446474, "dur": 32, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446509, "dur": 32, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446543, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446569, "dur": 27, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446599, "dur": 28, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446629, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446632, "dur": 41, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446675, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446678, "dur": 38, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446718, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446720, "dur": 38, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446761, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446763, "dur": 45, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446810, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446813, "dur": 129, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446946, "dur": 2, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545446950, "dur": 58, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447013, "dur": 48, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447063, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447066, "dur": 44, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447111, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447114, "dur": 36, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447151, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447153, "dur": 37, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447192, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447194, "dur": 38, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447235, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447237, "dur": 35, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447275, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447278, "dur": 50, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447330, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447333, "dur": 46, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447382, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447384, "dur": 41, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447433, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447434, "dur": 52, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447490, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447493, "dur": 51, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447546, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447548, "dur": 43, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447594, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447596, "dur": 67, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447671, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447674, "dur": 40, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447716, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447718, "dur": 34, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447754, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447756, "dur": 37, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447796, "dur": 7, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447804, "dur": 38, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447845, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447847, "dur": 37, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447886, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545447888, "dur": 288, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448179, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448183, "dur": 58, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448244, "dur": 3, "ph": "X", "name": "ProcessMessages 3115", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448249, "dur": 43, "ph": "X", "name": "ReadAsync 3115", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448294, "dur": 1, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448296, "dur": 38, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448336, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448338, "dur": 33, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448375, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448414, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448416, "dur": 42, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448460, "dur": 1, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448462, "dur": 39, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448503, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448505, "dur": 36, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448543, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448545, "dur": 33, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448582, "dur": 40, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448624, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448626, "dur": 50, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448679, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448683, "dur": 46, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448732, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448734, "dur": 32, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448770, "dur": 32, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448807, "dur": 42, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448851, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545448853, "dur": 41, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449119, "dur": 72, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449196, "dur": 5, "ph": "X", "name": "ProcessMessages 4222", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449202, "dur": 50, "ph": "X", "name": "ReadAsync 4222", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449256, "dur": 2, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449259, "dur": 36, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449298, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449299, "dur": 26, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449328, "dur": 41, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449371, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449410, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449412, "dur": 54, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449469, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449472, "dur": 40, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449514, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449516, "dur": 32, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449551, "dur": 32, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449589, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449591, "dur": 40, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449634, "dur": 34, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449670, "dur": 34, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449707, "dur": 76, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449788, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449839, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449842, "dur": 30, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449876, "dur": 24, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449902, "dur": 30, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449934, "dur": 31, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449967, "dur": 1, "ph": "X", "name": "ProcessMessages 97", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545449969, "dur": 30, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450002, "dur": 48, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450052, "dur": 29, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450084, "dur": 27, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450113, "dur": 42, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450157, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450185, "dur": 24, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450211, "dur": 26, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450239, "dur": 184, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450428, "dur": 55, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450485, "dur": 2, "ph": "X", "name": "ProcessMessages 1803", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450488, "dur": 50, "ph": "X", "name": "ReadAsync 1803", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450542, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450545, "dur": 49, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450597, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450600, "dur": 55, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450658, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450662, "dur": 35, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450698, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450700, "dur": 35, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450738, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450742, "dur": 29, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450773, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450775, "dur": 29, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450807, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450808, "dur": 42, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450855, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450858, "dur": 44, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450905, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450908, "dur": 45, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450955, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450958, "dur": 36, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450996, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545450998, "dur": 35, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451035, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451037, "dur": 28, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451069, "dur": 59, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451132, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451165, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451167, "dur": 35, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451204, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451206, "dur": 31, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451240, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451242, "dur": 38, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451284, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451285, "dur": 27, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451315, "dur": 42, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451359, "dur": 36, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451398, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451400, "dur": 42, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451444, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451447, "dur": 38, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451486, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451489, "dur": 111, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451602, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451604, "dur": 34, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451640, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451642, "dur": 34, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451678, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451680, "dur": 66, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451752, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451850, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451856, "dur": 59, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451917, "dur": 2, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545451921, "dur": 136, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452060, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452062, "dur": 68, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452139, "dur": 2, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452142, "dur": 61, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452206, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452209, "dur": 45, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452257, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452260, "dur": 55, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452319, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452321, "dur": 41, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452366, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452368, "dur": 39, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452412, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452469, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452471, "dur": 71, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452544, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452546, "dur": 86, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452635, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452636, "dur": 45, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452685, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452688, "dur": 135, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452856, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452860, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452905, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452907, "dur": 42, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452953, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452955, "dur": 36, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452993, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545452995, "dur": 34, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453041, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453043, "dur": 51, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453347, "dur": 2, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453351, "dur": 76, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453486, "dur": 3, "ph": "X", "name": "ProcessMessages 2072", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453491, "dur": 51, "ph": "X", "name": "ReadAsync 2072", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453544, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453546, "dur": 41, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453592, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453645, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453647, "dur": 35, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453685, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453688, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453739, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453769, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453772, "dur": 115, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453892, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453953, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545453956, "dur": 47, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454005, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454007, "dur": 43, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454054, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454056, "dur": 208, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454268, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454270, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454328, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454331, "dur": 48, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454382, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454385, "dur": 51, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454439, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454441, "dur": 79, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454523, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454527, "dur": 47, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454575, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454578, "dur": 64, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454645, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454648, "dur": 40, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454691, "dur": 40, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454733, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454735, "dur": 135, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454873, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545454878, "dur": 154, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455035, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455038, "dur": 50, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455090, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455093, "dur": 36, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455132, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455170, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455172, "dur": 34, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455208, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455210, "dur": 30, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455244, "dur": 116, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455365, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455403, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455405, "dur": 51, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455458, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455459, "dur": 144, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455608, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455647, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455649, "dur": 41, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455693, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455695, "dur": 30, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455727, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455729, "dur": 118, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455851, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455886, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455888, "dur": 31, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455921, "dur": 34, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455957, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455959, "dur": 37, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545455998, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456000, "dur": 33, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456036, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456038, "dur": 118, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456161, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456200, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456202, "dur": 56, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456260, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456262, "dur": 34, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456297, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456300, "dur": 107, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456412, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456449, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456452, "dur": 41, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456495, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456498, "dur": 42, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456543, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456545, "dur": 119, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456668, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456717, "dur": 7, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456725, "dur": 46, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456774, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456777, "dur": 37, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456815, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456817, "dur": 75, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456896, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456898, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456944, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456947, "dur": 32, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545456983, "dur": 30, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457015, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457017, "dur": 93, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457115, "dur": 168, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457285, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457288, "dur": 39, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457329, "dur": 2, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457332, "dur": 52, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457386, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457388, "dur": 50, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457441, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457443, "dur": 99, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457546, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457585, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457587, "dur": 40, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457629, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457631, "dur": 82, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457717, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457752, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457754, "dur": 37, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457793, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457795, "dur": 28, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457826, "dur": 72, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457902, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457938, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457940, "dur": 35, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457977, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545457979, "dur": 104, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458086, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458132, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458135, "dur": 42, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458179, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458180, "dur": 33, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458215, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458217, "dur": 85, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458310, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458350, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458351, "dur": 34, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458387, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458389, "dur": 31, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458422, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458424, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458503, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458546, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458548, "dur": 39, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458589, "dur": 7, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458597, "dur": 29, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458629, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458630, "dur": 93, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458728, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458770, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458772, "dur": 35, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458811, "dur": 26, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458840, "dur": 44, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458887, "dur": 36, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458925, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458927, "dur": 49, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458979, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545458982, "dur": 41, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459025, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459027, "dur": 34, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459065, "dur": 72, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459141, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459185, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459187, "dur": 35, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459226, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459229, "dur": 37, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459268, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459270, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459347, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459349, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459380, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459382, "dur": 33, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459417, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459419, "dur": 37, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459458, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459460, "dur": 82, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459544, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459580, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459582, "dur": 38, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459623, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459625, "dur": 34, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459671, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459674, "dur": 41, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459717, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459720, "dur": 101, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459826, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459871, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459873, "dur": 34, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459909, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459911, "dur": 36, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459949, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459951, "dur": 27, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459980, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545459981, "dur": 34, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460017, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460019, "dur": 31, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460054, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460082, "dur": 25, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460109, "dur": 6, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460117, "dur": 82, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460203, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460234, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460236, "dur": 38, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460277, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460278, "dur": 29, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460310, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460312, "dur": 109, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460426, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460471, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460473, "dur": 36, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460512, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460514, "dur": 37, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460553, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460555, "dur": 36, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460593, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460596, "dur": 37, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460635, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460637, "dur": 37, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460726, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460728, "dur": 40, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460771, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460773, "dur": 79, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460858, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460905, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460908, "dur": 39, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460951, "dur": 31, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460986, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545460989, "dur": 33, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461024, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461027, "dur": 93, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461125, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461238, "dur": 2, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461241, "dur": 52, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461295, "dur": 2, "ph": "X", "name": "ProcessMessages 1904", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461299, "dur": 36, "ph": "X", "name": "ReadAsync 1904", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461336, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461338, "dur": 34, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461376, "dur": 97, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461478, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461530, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461533, "dur": 41, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461582, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461585, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461636, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461638, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461677, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461679, "dur": 140, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461823, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461875, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461877, "dur": 43, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461922, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545461973, "dur": 47, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462022, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462025, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462060, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462063, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462101, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462103, "dur": 42, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462147, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462149, "dur": 35, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462187, "dur": 36, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462225, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462227, "dur": 85, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462316, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462366, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462369, "dur": 59, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462431, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462433, "dur": 37, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462474, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462476, "dur": 104, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462587, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462641, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462645, "dur": 35, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462683, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462685, "dur": 151, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462840, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462896, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462898, "dur": 45, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462945, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545462947, "dur": 69, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463018, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463020, "dur": 29, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463052, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463054, "dur": 116, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463173, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463176, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463227, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463229, "dur": 45, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463275, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463278, "dur": 29, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463311, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463313, "dur": 123, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463439, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463441, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463483, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463485, "dur": 167, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463656, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463659, "dur": 59, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463723, "dur": 2, "ph": "X", "name": "ProcessMessages 1731", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463727, "dur": 40, "ph": "X", "name": "ReadAsync 1731", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463771, "dur": 125, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463901, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463935, "dur": 4, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463940, "dur": 48, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545463992, "dur": 7, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464001, "dur": 50, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464053, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464055, "dur": 82, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464141, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464194, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464196, "dur": 41, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464240, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464242, "dur": 322, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464565, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464567, "dur": 51, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464621, "dur": 3, "ph": "X", "name": "ProcessMessages 2197", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464626, "dur": 38, "ph": "X", "name": "ReadAsync 2197", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464667, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464669, "dur": 55, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464726, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464729, "dur": 40, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464771, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464773, "dur": 116, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545464894, "dur": 239, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465135, "dur": 2, "ph": "X", "name": "ProcessMessages 1579", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465141, "dur": 44, "ph": "X", "name": "ReadAsync 1579", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465189, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465239, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465242, "dur": 53, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465298, "dur": 31, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465427, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465430, "dur": 45, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465478, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465480, "dur": 44, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465749, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545465753, "dur": 508, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545466265, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545466275, "dur": 51, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545466328, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545466331, "dur": 371, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545466706, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545466709, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545466774, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545466776, "dur": 58, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545466838, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545466840, "dur": 47, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467226, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467230, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467303, "dur": 2, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467306, "dur": 117, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467428, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467432, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467490, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467495, "dur": 49, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467547, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467550, "dur": 40, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467593, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467595, "dur": 145, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467744, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467750, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467807, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467813, "dur": 40, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467855, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467857, "dur": 112, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467973, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545467975, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468068, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468071, "dur": 50, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468123, "dur": 1, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468125, "dur": 134, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468262, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468265, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468484, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468491, "dur": 50, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468545, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468548, "dur": 314, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468866, "dur": 2, "ph": "X", "name": "ProcessMessages 1260", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545468870, "dur": 168, "ph": "X", "name": "ReadAsync 1260", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469041, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469043, "dur": 55, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469102, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469105, "dur": 41, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469151, "dur": 47, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469202, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469204, "dur": 164, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469372, "dur": 4, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469378, "dur": 75, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469459, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469525, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469529, "dur": 38, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469571, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469652, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469654, "dur": 50, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469707, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469714, "dur": 127, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469846, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469903, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469906, "dur": 47, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469956, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545469959, "dur": 38, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470001, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470047, "dur": 70, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470121, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470163, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470165, "dur": 37, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470205, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470207, "dur": 32, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470241, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470243, "dur": 94, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470343, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545470392, "dur": 956, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545471352, "dur": 148, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545472077, "dur": 178, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545472258, "dur": 12, "ph": "X", "name": "ProcessMessages 11911", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545472272, "dur": 141, "ph": "X", "name": "ReadAsync 11911", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545472421, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545472425, "dur": 2222, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545474654, "dur": 7, "ph": "X", "name": "ProcessMessages 2114", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545474663, "dur": 225, "ph": "X", "name": "ReadAsync 2114", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545474893, "dur": 5, "ph": "X", "name": "ProcessMessages 3593", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545474975, "dur": 435, "ph": "X", "name": "ReadAsync 3593", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545475415, "dur": 901, "ph": "X", "name": "ProcessMessages 6073", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545476322, "dur": 913, "ph": "X", "name": "ReadAsync 6073", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545477240, "dur": 26, "ph": "X", "name": "ProcessMessages 3392", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545477271, "dur": 439, "ph": "X", "name": "ReadAsync 3392", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545477931, "dur": 6, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545477941, "dur": 122, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545478321, "dur": 14, "ph": "X", "name": "ProcessMessages 1920", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545478336, "dur": 712, "ph": "X", "name": "ReadAsync 1920", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545479053, "dur": 4, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545479058, "dur": 540, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545479602, "dur": 6, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545479616, "dur": 792, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545480414, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545480418, "dur": 10485, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545490913, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545490918, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545490964, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545490967, "dur": 207, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545491178, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545491180, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545491231, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545491234, "dur": 1086, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545492326, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545492328, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545492410, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545492416, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545492466, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545492468, "dur": 5386, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498386, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498392, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498474, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498478, "dur": 138, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498624, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498626, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498656, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498657, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498743, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498745, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545498774, "dur": 270, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499052, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499082, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499129, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499132, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499199, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499201, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499241, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499243, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499338, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499370, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499372, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499434, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499473, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499476, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499576, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499611, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499642, "dur": 340, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545499986, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500027, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500030, "dur": 32, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500065, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500067, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500249, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500284, "dur": 310, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500599, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500633, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500635, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500672, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500676, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500709, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500713, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500748, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500870, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500872, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500903, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500905, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545500937, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501029, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501032, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501088, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501122, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501124, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501161, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501163, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501208, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501240, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501242, "dur": 394, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501641, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501684, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501687, "dur": 37, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501728, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501730, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501834, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501851, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501881, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501883, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501927, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501962, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545501964, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502009, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502047, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502048, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502106, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502143, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502145, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502179, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502181, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502214, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502248, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502251, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502286, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502289, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502326, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502357, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502359, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502404, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502433, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502492, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502524, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502526, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502625, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502659, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502691, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502693, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502737, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502769, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502799, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502838, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502840, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502920, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502959, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502961, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502995, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545502997, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503029, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503067, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503072, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503332, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503338, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503384, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503386, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503426, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503429, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503481, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503483, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503517, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503519, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503656, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503691, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503693, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503773, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503806, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503812, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503850, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503852, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503886, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503921, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503957, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545503964, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504004, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504008, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504055, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504059, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504094, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504096, "dur": 554, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504659, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504708, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504712, "dur": 121, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504837, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504839, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504897, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545504899, "dur": 181, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505084, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505086, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505128, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505130, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505167, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505169, "dur": 457, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505630, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505632, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505679, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505754, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505802, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545505804, "dur": 542, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545506350, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545506353, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545506402, "dur": 1125, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545507873, "dur": 111, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545507988, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545507993, "dur": 477, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545508474, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545508479, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545508527, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545508529, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545508583, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545508585, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545508745, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545508793, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545508795, "dur": 164, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545508966, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509006, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509008, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509050, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509055, "dur": 396, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509455, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509458, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509501, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509504, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509559, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509561, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509594, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509596, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509629, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509766, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509768, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509799, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509801, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509836, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545509837, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545510002, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545510041, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545510043, "dur": 3893, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545513945, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545513950, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545514042, "dur": 3, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545514047, "dur": 1293, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545515363, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545515368, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545515402, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545515407, "dur": 99794, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545615285, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545615291, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545615912, "dur": 9494, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545625415, "dur": 2329, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545627753, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545627757, "dur": 691, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545628452, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545628456, "dur": 40, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545628500, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545628503, "dur": 1517, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545630025, "dur": 692, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545630721, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545630723, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545630765, "dur": 359, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545631127, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545631167, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545631170, "dur": 2008, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633183, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633186, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633232, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633236, "dur": 464, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633705, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633746, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633748, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633810, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633813, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633849, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545633852, "dur": 1436, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545635293, "dur": 183, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545635480, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545635484, "dur": 969, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545636456, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545636459, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545636497, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545636500, "dur": 499, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545637425, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545637501, "dur": 294, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545637799, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545637801, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545637848, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545637850, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545637917, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545637919, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545637990, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545638021, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545638023, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545638057, "dur": 784, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545638845, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545638847, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545638887, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545638890, "dur": 46, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545639387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545639389, "dur": 131, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545639524, "dur": 4, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545639530, "dur": 38, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545639572, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545639592, "dur": 303, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545639899, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545639902, "dur": 1188, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545641097, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545641102, "dur": 284, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545641390, "dur": 8, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545641496, "dur": 51, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545641551, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545641554, "dur": 402, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545641962, "dur": 642, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545642609, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545642614, "dur": 58, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545642928, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114545642932, "dur": 393103, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546036047, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546036053, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546036106, "dur": 34, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546036142, "dur": 8700, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546044849, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546044855, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546044936, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546044939, "dur": 83139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546128091, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546128097, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546128143, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546128150, "dur": 246, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546128402, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546128404, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546128465, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546128469, "dur": 141385, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546269876, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546269885, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546269944, "dur": 36, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546269982, "dur": 7141, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546277136, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546277141, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546277225, "dur": 34, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546277260, "dur": 25721, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546302995, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546303001, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546303050, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546303053, "dur": 6792, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546309862, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546309869, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546309923, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546309929, "dur": 2483, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546312424, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546312431, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546312469, "dur": 33, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546312503, "dur": 170548, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546483064, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546483070, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546483115, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546483121, "dur": 783, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546483911, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546483916, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546483979, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546484009, "dur": 45184, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546529206, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546529219, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546529254, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546529257, "dur": 872, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546530136, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546530139, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546530186, "dur": 394, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 24492, "tid": 12884901888, "ts": 1754114546530583, "dur": 19369, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 24492, "tid": 637, "ts": 1754114546576284, "dur": 3134, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 24492, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 24492, "tid": 8589934592, "ts": 1754114545399676, "dur": 428460, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 24492, "tid": 8589934592, "ts": 1754114545828141, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 24492, "tid": 8589934592, "ts": 1754114545828147, "dur": 1472, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 24492, "tid": 637, "ts": 1754114546579425, "dur": 15, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 24492, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 24492, "tid": 4294967296, "ts": 1754114545354007, "dur": 1198312, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 24492, "tid": 4294967296, "ts": 1754114545361471, "dur": 13084, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 24492, "tid": 4294967296, "ts": 1754114546552667, "dur": 7158, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 24492, "tid": 4294967296, "ts": 1754114546556929, "dur": 178, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 24492, "tid": 4294967296, "ts": 1754114546559985, "dur": 22, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 24492, "tid": 637, "ts": 1754114546579442, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754114545423808, "dur": 82, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754114545423939, "dur": 3260, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754114545427226, "dur": 2332, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754114545429768, "dur": 106, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754114545429875, "dur": 928, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754114545430861, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1754114545432488, "dur": 433, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754114545434191, "dur": 2900, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_22FDE6BD161A4064.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754114545437133, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754114545437885, "dur": 252, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754114545439044, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754114545439613, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754114545439670, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754114545439796, "dur": 275, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754114545440784, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754114545441324, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754114545442886, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754114545443802, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754114545444129, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754114545444986, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754114545446298, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754114545446896, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754114545447448, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754114545448239, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754114545450621, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754114545452100, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754114545452680, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754114545452900, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754114545453095, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754114545453189, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754114545453316, "dur": 200, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754114545453992, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754114545454914, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/AstarPathfindingProject.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754114545455083, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/AstarPathfindingProject.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754114545455503, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754114545456493, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754114545457541, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754114545457985, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754114545461506, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754114545463574, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754114545465214, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Animation.Rigging.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754114545466117, "dur": 770, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754114545467333, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754114545468082, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754114545469433, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754114545470173, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754114545470958, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754114545471419, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754114545472729, "dur": 171, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754114545430847, "dur": 43418, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754114545474284, "dur": 1055562, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754114546529847, "dur": 256, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754114546530105, "dur": 189, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754114546530555, "dur": 113, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754114546530718, "dur": 1782, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754114545430734, "dur": 43564, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545474336, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545474452, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_4D5EF4ECE256D09B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754114545475095, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_94383F50AE2F3B27.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754114545475336, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545475729, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E120A74A668A048A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754114545476030, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D282260B0083D8AB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754114545476141, "dur": 322, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D282260B0083D8AB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754114545476695, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754114545477150, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545477360, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545477548, "dur": 546, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754114545478096, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754114545478652, "dur": 312, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754114545478994, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545479176, "dur": 1412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545480594, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545481582, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545481748, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545482191, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545482984, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545483621, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545485098, "dur": 940, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Inspector\\PropertyDrawers\\CubemapPropertyDrawer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754114545484828, "dur": 1701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545486529, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545488314, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545488540, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545488796, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545489127, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545489846, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545490239, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545490655, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545491388, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545492538, "dur": 1834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545494373, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545494848, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545495287, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545495525, "dur": 904, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@e2c83221d2dc\\InputSystem\\Devices\\InputDevice.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754114545495513, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545496762, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545497901, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545498486, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545499487, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754114545499620, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545499710, "dur": 1678, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754114545501404, "dur": 838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754114545502243, "dur": 650, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545502947, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754114545503855, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545503937, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545504120, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754114545504783, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754114545505313, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545505483, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545505819, "dur": 6984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545512805, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754114545513013, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545513071, "dur": 114702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545627790, "dur": 2466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754114545630258, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545630388, "dur": 2996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Animation.Rigging.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754114545633385, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545633545, "dur": 4503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754114545638049, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545638155, "dur": 3594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754114545641751, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545641924, "dur": 193298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754114545835223, "dur": 694660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545430826, "dur": 43519, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545474355, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B4A1BF472DE35F91.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754114545474850, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545474919, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_5E49D6BEFCDFF46D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754114545475018, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_68DB32F33070957F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754114545475072, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545475161, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_84BF5D8C55DA5D3D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754114545475360, "dur": 484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545475854, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2039BDFA7AABC76E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754114545475922, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545476281, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545476696, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754114545476821, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754114545477237, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754114545477504, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754114545477874, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545477941, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PackageToolsEditor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754114545478155, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545478601, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754114545478841, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545479357, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754114545479515, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545479787, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545480015, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545480893, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545481939, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545482145, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545482790, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\EditorClipFactory.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754114545482500, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545483549, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545485084, "dur": 692, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Controls\\EnumControl.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754114545484831, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545486147, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545487168, "dur": 1057, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Graphs\\GraphDataUtils.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754114545486827, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545488668, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545489100, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545490000, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545490556, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545492027, "dur": 595, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Canvases\\VisualScriptingCanvas.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754114545491374, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545492967, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545494074, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545495265, "dur": 942, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\Attributes\\TimelineHelpURLAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754114545494499, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545496376, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545496755, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545497863, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545498516, "dur": 1154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545499671, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754114545499881, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545500060, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754114545502449, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545502601, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754114545502806, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754114545503476, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545503608, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545504174, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545505139, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545505779, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754114545506369, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754114545506822, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545506966, "dur": 5869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545512836, "dur": 112668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545625506, "dur": 2508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754114545628016, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545628135, "dur": 3356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754114545631493, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545631559, "dur": 2547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754114545634107, "dur": 1621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545635738, "dur": 2650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PackageToolsEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754114545638393, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545638907, "dur": 3384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754114545642292, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754114545642409, "dur": 887481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545430734, "dur": 43583, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545474338, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545474532, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FA7F2E82B1549095.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754114545475419, "dur": 385, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FA7F2E82B1549095.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754114545475808, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F4B95EF39F1A8010.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754114545475910, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_A38368206D763C77.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754114545476108, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545476230, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_06983A7F9FE1EEBF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754114545476554, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_78BD15807B404F33.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754114545476677, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754114545476791, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754114545476931, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545477316, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545477495, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754114545477696, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545477831, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545477927, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754114545478410, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545478600, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754114545478843, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545479175, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545479432, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545479964, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545480855, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545481742, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.43f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-interlocked-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754114545481571, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545482702, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545483587, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545484757, "dur": 1038, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Extensions\\FieldExtensions.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754114545484424, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545485878, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545486410, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545487290, "dur": 808, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.State\\States\\SuperStateWidget.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754114545487290, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545488495, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545488800, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545489698, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545490404, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545491181, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545492230, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545493630, "dur": 645, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionStay2DMessageListener.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754114545494303, "dur": 836, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionExit2DMessageListener.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754114545495169, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionEnter2DMessageListener.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754114545492891, "dur": 2843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545495734, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545496194, "dur": 662, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\History\\SaveAction.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754114545495939, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545496879, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545497899, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545498490, "dur": 937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545499429, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754114545499660, "dur": 887, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754114545500548, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754114545501267, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545501428, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545501938, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754114545502136, "dur": 2274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754114545504412, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545504536, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545505137, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545505775, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754114545506178, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754114545506666, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545506815, "dur": 6005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545512821, "dur": 112686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545625508, "dur": 2505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754114545628015, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545628144, "dur": 2037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754114545630182, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545630347, "dur": 2410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754114545632758, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545632940, "dur": 3283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754114545636224, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545636673, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754114545639222, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545639344, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545639501, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754114545639698, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545639812, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545640203, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545640301, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545640488, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545641022, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545641389, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754114545642386, "dur": 887455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545430784, "dur": 43546, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545474340, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545474995, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545475098, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E7EDEE91A237FB4D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545475314, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545475396, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_F7B8A20762668D38.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545475462, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_95CE26B7D8385BCA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545475631, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B1A10C5722B27484.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545475872, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_EF67977AD200318E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545476097, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_EF67977AD200318E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545476257, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_52C906CE0C7BEE08.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545476513, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754114545476613, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545476828, "dur": 457, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545477401, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754114545477510, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754114545477930, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754114545478545, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545478630, "dur": 328, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754114545478992, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545479172, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754114545479416, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545479758, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545480027, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545481251, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545481910, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545482141, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545482717, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Signals\\SignalAssetInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754114545484288, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\MenuPriority.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754114545482391, "dur": 2631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545485083, "dur": 714, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Util\\SpaceTransformUtil.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754114545485022, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545486445, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545487429, "dur": 1082, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.State\\Graph\\StateGraphContext.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754114545487353, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545489119, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545489849, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545490149, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545490374, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545491794, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545492036, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545492266, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545493015, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545493884, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545494093, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545495040, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545495704, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545495934, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545496235, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545496573, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545497586, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545498474, "dur": 1560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545500035, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545500233, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545500421, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545500631, "dur": 2125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754114545502757, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545502846, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545503250, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545503410, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754114545503697, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545504173, "dur": 950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545505123, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545505790, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754114545506207, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754114545507244, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545507376, "dur": 5449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545512825, "dur": 112690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545625517, "dur": 3009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754114545628528, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545628636, "dur": 2511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754114545631152, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545631429, "dur": 3758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754114545635188, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545635245, "dur": 3272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754114545638518, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545638685, "dur": 4137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Animation.Rigging.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754114545642823, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754114545642976, "dur": 886934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545430869, "dur": 43492, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545474371, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BEBB047E8EA0A5FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545475050, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545475115, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_ADF67561AEE86815.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545475171, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545475350, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545475659, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_CAECCED5B87D25B6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545475862, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_5E20C3D8D90E43EC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545476085, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_5E20C3D8D90E43EC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545476221, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_B522ABDF46A7E881.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545476739, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_5A43EC8C14EFD6DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545476830, "dur": 471, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_5A43EC8C14EFD6DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545477340, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545477521, "dur": 546, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754114545478432, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754114545478816, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545479138, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754114545479394, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545479488, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545479849, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545480392, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545481946, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545482858, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\EditMode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754114545483511, "dur": 1110, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\AddDelete\\AddDeleteItemModeRipple.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754114545484622, "dur": 1323, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\AddDelete\\AddDeleteItemModeReplace.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754114545482448, "dur": 3769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545486217, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545487661, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545488473, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545488995, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545490067, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545490396, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545490943, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545491948, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@c82f62fefab3\\Runtime\\TMP\\TMP_MaterialManager.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754114545491586, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545493106, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545494242, "dur": 579, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Collections\\DebugDictionary.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754114545493692, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545494851, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545495091, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545495320, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545495663, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545495955, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545496283, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545496699, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545496770, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545497665, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545498482, "dur": 2155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545500638, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754114545501128, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545501241, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/AstarPathfindingProjectEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545501441, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545501782, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545502101, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754114545503178, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545503297, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545503660, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545503904, "dur": 884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754114545504789, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545505032, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545505117, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545505797, "dur": 3554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545509353, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754114545509574, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754114545510081, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545510228, "dur": 2618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545512846, "dur": 115730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545628842, "dur": 3894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754114545632737, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545632944, "dur": 5541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754114545638491, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545638621, "dur": 3150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754114545641773, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754114545642294, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754114545642405, "dur": 887434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545430919, "dur": 43456, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545474387, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545474987, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_1EA6BBE2456BF314.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545475089, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_1A11F72A8792752A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545475358, "dur": 740, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545476098, "dur": 422, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_0368709244A973A5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545476699, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754114545477258, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754114545477499, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754114545477840, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754114545478050, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754114545478605, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754114545478805, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545479131, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18315634737643473047.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754114545479205, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754114545479451, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545479507, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545480580, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545481821, "dur": 712, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.43f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Authorization.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754114545481280, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545482835, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@d680fce5716f\\Editor\\VFXGraph\\VFXAbstractParticleURPLitOutput.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754114545482708, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545483707, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545483982, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545485089, "dur": 695, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\SearchWindowProvider.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754114545484592, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545485824, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545487327, "dur": 795, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Graphs\\MipmapStreamingShaderProperties.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754114545486753, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545488227, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545489458, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545489662, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545489929, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545490214, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545491032, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545492160, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545492759, "dur": 672, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticFunctionInvoker_0.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754114545492464, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545494251, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Decorators\\ValueAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754114545493607, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545495086, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545495314, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545495555, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545495767, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545495964, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545496232, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545496486, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545496752, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545497600, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545498473, "dur": 958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545499433, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545499607, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545499672, "dur": 1590, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545501264, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545502360, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545502493, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545502733, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545503285, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545503458, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545503703, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.DocCodeExamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545503895, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.DocCodeExamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545504448, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545504677, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545505241, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545505783, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545506643, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545507130, "dur": 882, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545508014, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545508156, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545508344, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545509228, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545509391, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545509566, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545509660, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545510276, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545510437, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545510641, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545511279, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545511357, "dur": 1449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545512810, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754114545513077, "dur": 112464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545625550, "dur": 2496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545628047, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545628159, "dur": 2341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Animation.Rigging.DocCodeExamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545630501, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545630653, "dur": 2595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545633250, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545633327, "dur": 2683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545636011, "dur": 1026, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545637044, "dur": 2814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754114545639860, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545640154, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545640359, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545640533, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545640871, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545641097, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545641553, "dur": 1345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754114545642937, "dur": 886968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545430972, "dur": 43425, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545474410, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_3397DEB3C6437C6C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545475028, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_42CC435405CEA14C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545475159, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6675A272C2C435FC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545475227, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545475414, "dur": 421, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6675A272C2C435FC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545475840, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_03ECB0133E6B6EED.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545475899, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545476824, "dur": 532, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754114545477495, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754114545477813, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754114545477961, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545478203, "dur": 676, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754114545478913, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545479165, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545479404, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545480042, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545480845, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545481953, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545482189, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545482671, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545483505, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545485066, "dur": 948, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Views\\Slots\\ColorRGBSlotControlView.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754114545484473, "dur": 1920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545486393, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545487322, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@d680fce5716f\\Runtime\\2D\\Shadows\\ShadowProvider\\ShadowUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754114545487033, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545487884, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545488493, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545488861, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545489619, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545489840, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545491045, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Utilities\\ProgressUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754114545490245, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545491618, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545492153, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545492484, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545493308, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545494347, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545494598, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545495206, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545495484, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545496223, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545496524, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545496806, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545497597, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545498475, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545499403, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545499608, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545499662, "dur": 901, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545500565, "dur": 1812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754114545502378, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545502486, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545502592, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545502777, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754114545503791, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545504037, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545504116, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545504350, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545504434, "dur": 1097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754114545505532, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545505690, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545505779, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545506774, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754114545507342, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545507501, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545507709, "dur": 1224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754114545508935, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545509142, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754114545509302, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545509372, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754114545509861, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545510028, "dur": 2816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545512845, "dur": 116022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545628869, "dur": 2554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754114545631425, "dur": 1799, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545633233, "dur": 3592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754114545636826, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545636886, "dur": 3716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754114545640603, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545640825, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545641182, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545641601, "dur": 188648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545832518, "dur": 523, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1754114545833042, "dur": 1999, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1754114545835042, "dur": 161, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1754114545830251, "dur": 4965, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754114545835217, "dur": 694683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545431018, "dur": 43402, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545474434, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_0C1C2D14907EB90E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754114545475101, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C49902DAED8AF95B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754114545475441, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_774CD8E5A270CA86.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754114545475493, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545475611, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_0EF0635D76D99CD0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754114545475702, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_0EF0635D76D99CD0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754114545475979, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8E726FC19BF52AA2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754114545476358, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545476699, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754114545476798, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754114545476922, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545477485, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754114545478303, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545478428, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754114545478610, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14093724650087468856.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754114545478927, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545479179, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545479594, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545479891, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545480583, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545481869, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545482175, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545482742, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545485030, "dur": 906, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Generation\\Enumerations\\KeywordDefinition.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754114545484096, "dur": 1995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545486158, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Nodes\\Input\\Texture\\TexelSizeNode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754114545486091, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545487544, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545488788, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545489621, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545490081, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545490336, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545491279, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Description\\MachineDescriptor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754114545491279, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545493870, "dur": 830, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754114545492613, "dur": 2243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545494857, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545495083, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545495289, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545495577, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545495776, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545496134, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\UI\\UnityPlasticTimer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754114545495989, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545497008, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545497758, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545498485, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545499413, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754114545499657, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754114545500304, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545500453, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754114545501157, "dur": 1077, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545502277, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754114545502871, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545503143, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545503575, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545504171, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545505172, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545505807, "dur": 7044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545512852, "dur": 115742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545628598, "dur": 2224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754114545630823, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545631013, "dur": 3263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754114545634277, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545634433, "dur": 2538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754114545636973, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545637049, "dur": 4319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754114545641369, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545641534, "dur": 1082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754114545642646, "dur": 887249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545431059, "dur": 43474, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545474535, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7C852BDA8365271E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545475137, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545475337, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545475659, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E79D22299C4825D4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545475893, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0B3C92E79B34D00C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545476077, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545476212, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3DE1FE7E9BAAF7A9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545476447, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545476788, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545476917, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545477002, "dur": 15708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754114545492712, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545492933, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545493057, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545494774, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\TimelineAttributes.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754114545495297, "dur": 912, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\TimelineAsset_CreateRemove.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754114545494416, "dur": 2239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545496655, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545497328, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545497859, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545498491, "dur": 1245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545499738, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PackageToolsEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545499958, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545500197, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545500404, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545500568, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545500623, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754114545501264, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545501727, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545501829, "dur": 1645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754114545503475, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545503599, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545504138, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545505178, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545505802, "dur": 7007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545512812, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754114545513036, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545513093, "dur": 112432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545625537, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754114545628065, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545628166, "dur": 5107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754114545633275, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545633355, "dur": 3687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754114545637044, "dur": 724, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545637774, "dur": 3967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754114545641743, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114545642023, "dur": 661281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754114546303336, "dur": 226367, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754114546303307, "dur": 226399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754114545431132, "dur": 43320, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545474464, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_DDBE5B8EC557ADE5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545475132, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_9C249CA42F8C791A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545475351, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545475927, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4A1C632ABDE44C9A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545476387, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EFCFBB64C82BDA36.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545476596, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545476829, "dur": 464, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545477298, "dur": 13936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545491235, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545491561, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545491790, "dur": 6552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545498344, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545498556, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545498712, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545499163, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545499273, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545499425, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545499593, "dur": 1062, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545500662, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545501450, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545501661, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545501723, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545502020, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545502569, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545502650, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545502730, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545502917, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754114545503112, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545503536, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545503732, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545504172, "dur": 929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545505131, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545505926, "dur": 6892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545512818, "dur": 112694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545625514, "dur": 3167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/AstarPathfindingProject.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545628682, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545628743, "dur": 2304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545631048, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545631218, "dur": 2744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545633963, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545634289, "dur": 3910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545638200, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545638459, "dur": 2826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754114545641286, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545641385, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754114545642295, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754114545642381, "dur": 887462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545431184, "dur": 43287, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545474482, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9DF987BA9AE65833.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754114545475155, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4FC0F931AAC867AE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754114545475264, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_F82B715478DE33F3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754114545475321, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545475498, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_36EDDF0496264E2B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754114545475698, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_36EDDF0496264E2B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754114545475963, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_196389E2854E5BDF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754114545476675, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754114545477256, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545477357, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545477494, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754114545477996, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754114545478157, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545478430, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754114545478810, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545479407, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545480542, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545481483, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545482392, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545482887, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545484022, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545485063, "dur": 731, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Inspector\\PropertyDrawers\\ShaderInputPropertyDrawer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754114545484823, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545485977, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545486748, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545487758, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545488268, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545488987, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545489502, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545489742, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545490152, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545490378, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545491003, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545491709, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545491922, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545492137, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545492744, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545494177, "dur": 1016, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\Keyframe_DirectConverter.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754114545495223, "dur": 968, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\GUIStyle_DirectConverter.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754114545496192, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\GUIStyleState_DirectConverter.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754114545493378, "dur": 3633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545497012, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545497973, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545498496, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545499420, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754114545499582, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545499658, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754114545500238, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545500506, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545500649, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754114545500868, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754114545501617, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545501846, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545502309, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754114545502554, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754114545503100, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545503594, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545503738, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545504118, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754114545504391, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754114545504861, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545505101, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1754114545505793, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545506189, "dur": 203, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545506987, "dur": 108586, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1754114545625511, "dur": 2677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754114545628189, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545628270, "dur": 2378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754114545630649, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545630845, "dur": 2534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754114545633380, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545633505, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754114545635949, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545636022, "dur": 2497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754114545638521, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545638698, "dur": 2495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754114545641194, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545641306, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114545641927, "dur": 402492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114546044450, "dur": 80089, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754114546044422, "dur": 81964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754114546128608, "dur": 358, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754114546129487, "dur": 148218, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754114546310130, "dur": 173437, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754114546310112, "dur": 173457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754114546483606, "dur": 915, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754114546484530, "dur": 45317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545431224, "dur": 43271, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545474538, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_204AB48AF9442CC2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545475221, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545475274, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545475329, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545475703, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_8548D09514643BAC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545475992, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0EB311F2080B4776.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545476170, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545476345, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_326BFA302771D2EF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545476633, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754114545476783, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545476857, "dur": 476, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545477344, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545477552, "dur": 535, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754114545478150, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545478608, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754114545478892, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545479218, "dur": 1289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545480524, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545481781, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545482217, "dur": 1694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545485076, "dur": 713, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Generation\\Utils\\TargetUtils.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754114545483913, "dur": 2034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545485947, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545487001, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545487321, "dur": 1126, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.State\\StateRevealCondition.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754114545487321, "dur": 2075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545489397, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545490011, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545491433, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Meta\\ProjectSettingMetadata.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754114545490599, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545492069, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545492310, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545494164, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545494884, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545495077, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545495267, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545495663, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545495854, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545496090, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545496347, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545496556, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545497220, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545497749, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545498481, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545499406, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545499594, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545499651, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545499757, "dur": 1405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754114545501163, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545501324, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545501865, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545502462, "dur": 1907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754114545504370, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545504601, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545504835, "dur": 1700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754114545506537, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545506826, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545507031, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754114545507598, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545507713, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545507971, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754114545508101, "dur": 4739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545512840, "dur": 115064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545627918, "dur": 2624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754114545630544, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545631021, "dur": 4172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754114545635195, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545635482, "dur": 2849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754114545638336, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545638607, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754114545638660, "dur": 4144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754114545642807, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754114545642918, "dur": 886985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545431262, "dur": 43259, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545474523, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_233FB08ADCB5306F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754114545475318, "dur": 719, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545476048, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1A6A5C7C900791C9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754114545476153, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8F85FFFDEEDD670C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754114545476432, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.APIComparison.Framework.dll_795DFBA713DFDA95.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754114545476691, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754114545476927, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545477091, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754114545477164, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545477266, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754114545477505, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754114545477945, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754114545478053, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545478286, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545478420, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.DocCodeExamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754114545478629, "dur": 309, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754114545478970, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545479365, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545480396, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Assets\\AstarPathfindingProject\\Utilities\\AstarParallel.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754114545479950, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545481244, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545482331, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545483629, "dur": 803, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@d680fce5716f\\Editor\\2D\\ShapeEditor\\EditablePath\\EditablePathExtensions.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754114545485085, "dur": 705, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@d680fce5716f\\Editor\\2D\\Shadows\\CastingSourceDropDown.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754114545483629, "dur": 2303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545485932, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545487239, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545488177, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545489027, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545490210, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545490617, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545492049, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545492933, "dur": 613, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Platforms\\PlatformUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754114545493717, "dur": 1143, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Machines\\Machine.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754114545495067, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnScrollMessageListener.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754114545492673, "dur": 3160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545495833, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545496044, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545497099, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545497642, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545498476, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545499410, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754114545500055, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754114545500729, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545501283, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545501549, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AstarPathfindingProjectEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754114545502333, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545502562, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545502647, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754114545502874, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754114545503429, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545503706, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545503777, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754114545503957, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545504042, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754114545504996, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545505165, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545505803, "dur": 6996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545512802, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754114545512971, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545513035, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754114545513615, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545513715, "dur": 111794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545625524, "dur": 2519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754114545628045, "dur": 556, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545628607, "dur": 2311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754114545630924, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545631006, "dur": 4107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754114545635115, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545635193, "dur": 2682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754114545637877, "dur": 606, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545638491, "dur": 4013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/AstarPathfindingProjectEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754114545642506, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754114545642679, "dur": 887218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545431336, "dur": 43215, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545474560, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_7FFEF131F3AD3BA4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754114545475400, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_7FFEF131F3AD3BA4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754114545475480, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_88FDBEF459069934.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754114545475591, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_53CC86BB524A4E38.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754114545475677, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_53CC86BB524A4E38.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754114545475946, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_D77C7CCB1D31CDA5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754114545476806, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754114545477098, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754114545477346, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545477528, "dur": 440, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754114545478006, "dur": 506, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754114545478628, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754114545478945, "dur": 1274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545480227, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545480888, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545481699, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545481918, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545482196, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545482939, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545483388, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545485098, "dur": 851, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\BuiltInLitGUI.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754114545483977, "dur": 1972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545485950, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545486899, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545487642, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545488499, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545488796, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545489094, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545490095, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545490323, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545491592, "dur": 819, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@c82f62fefab3\\Runtime\\TMP\\TMP_TextElement.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754114545492623, "dur": 775, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@c82f62fefab3\\Runtime\\TMP\\TMP_SubMeshUI.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754114545491473, "dur": 2331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545493805, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545494991, "dur": 564, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance@fb0dc592af8b\\Runtime\\Data\\Project.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754114545494646, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545495651, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545495858, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545496071, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545496514, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545496896, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545497769, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545498483, "dur": 1155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545499639, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/AstarPathfindingProject.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754114545499836, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AstarPathfindingProject.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754114545500436, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545500535, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PackageToolsEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754114545501075, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545501216, "dur": 1188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754114545502404, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545502766, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754114545502994, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545503341, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754114545504049, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545504276, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545505249, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545505924, "dur": 6891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545512815, "dur": 116038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545628865, "dur": 2451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754114545631317, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545631385, "dur": 4379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754114545635765, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545636076, "dur": 2775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754114545638852, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545638939, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545639035, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545639529, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754114545639786, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545640124, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545640224, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545640529, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545640773, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545640923, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545641016, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545641321, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114545642031, "dur": 668090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754114546310162, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754114546310124, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754114546310414, "dur": 2585, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754114546313009, "dur": 216852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545431372, "dur": 43172, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545474546, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_12B7B81D94FFC9B7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545475401, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_12B7B81D94FFC9B7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545475632, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_ABDE5DF73D4F51D2.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545475823, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_CA1900490E2D89BF.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545475876, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545476089, "dur": 303, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_CA1900490E2D89BF.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545476809, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545476950, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545477325, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545477512, "dur": 345, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754114545477934, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754114545478423, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754114545478623, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754114545478939, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545479193, "dur": 1136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545480336, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545481380, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545482124, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545482682, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545483887, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545485054, "dur": 931, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Manipulators\\ResizeBorderFrame.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754114545484813, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545486414, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545487242, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545487900, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545489407, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545489638, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545490040, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545490258, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545491299, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545491785, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545492013, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545492784, "dur": 708, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Unity\\RequiresUnityAPIAttribute.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754114545492295, "dur": 2127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545495013, "dur": 1878, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\Extensions\\TrackExtensions.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754114545494422, "dur": 3119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545497599, "dur": 872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545498519, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545499450, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545499591, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545499643, "dur": 1000, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545500645, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114545501144, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545501291, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545501694, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545501772, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545502123, "dur": 1577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114545503700, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545503825, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545504137, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545504653, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545504920, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114545507594, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545507878, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545507949, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545508160, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114545509055, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545509147, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545509349, "dur": 1887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114545511238, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545511407, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545511595, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114545512557, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545512822, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754114545513040, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114545513720, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545513842, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114545514338, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545514464, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114545515700, "dur": 158, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114545516900, "dur": 519728, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114546045020, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754114546044411, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754114546045290, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114546046182, "dur": 78375, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754114546045431, "dur": 80700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754114546127935, "dur": 575, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114546270224, "dur": 189, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114546129345, "dur": 141101, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754114546303315, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1754114546303295, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1754114546303475, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754114546303558, "dur": 226317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545431433, "dur": 43138, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545474572, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_F66768BDA37A5632.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545475225, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_D05A0C289995407B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545475329, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545475625, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_64D3851878536F69.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545475728, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_64D3851878536F69.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545476008, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1E332B821429B7DB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545476441, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545476706, "dur": 429, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754114545477137, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754114545477336, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545477485, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754114545478384, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545478438, "dur": 304, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754114545478841, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545479132, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754114545479229, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545479412, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545480550, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545481632, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545481857, "dur": 2281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545484138, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545485086, "dur": 691, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Drawing\\Colors\\IColorProvider.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754114545484935, "dur": 2189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545487124, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545488482, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545488964, "dur": 1946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545490911, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545492217, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545492537, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545493349, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545494364, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545494791, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545495070, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545495293, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545495523, "dur": 644, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@e2c83221d2dc\\InputSystem\\Editor\\Analytics\\InputActionsEditorSessionAnalytic.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754114545496190, "dur": 780, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@e2c83221d2dc\\InputSystem\\Devices\\Touchscreen.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754114545495508, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545497161, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545498007, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545498479, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545499422, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545499754, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545499832, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754114545500385, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545500564, "dur": 990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754114545501555, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545501951, "dur": 1351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754114545503303, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545503407, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545503702, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545504136, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545504549, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754114545505158, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545505299, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545505786, "dur": 3388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545509182, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545509344, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545509531, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545509591, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754114545510276, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545510425, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754114545510614, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754114545511086, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545511234, "dur": 1623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545512858, "dur": 115256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545628128, "dur": 5812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754114545633941, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545634033, "dur": 2976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754114545637017, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545637623, "dur": 3868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754114545641492, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545641598, "dur": 1362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754114545642994, "dur": 886922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754114546544623, "dur": 3585, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 24492, "tid": 637, "ts": 1754114546580397, "dur": 4173, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 24492, "tid": 637, "ts": 1754114546584640, "dur": 4602, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 24492, "tid": 637, "ts": 1754114546571668, "dur": 19265, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}