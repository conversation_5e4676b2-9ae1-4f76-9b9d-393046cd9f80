{"format": 1, "restore": {"S:\\Unity Projects\\Twistoria\\Unity.PerformanceTesting.csproj": {}}, "projects": {"S:\\Unity Projects\\Twistoria\\Unity.PerformanceTesting.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.PerformanceTesting.csproj", "projectName": "Unity.PerformanceTesting", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PerformanceTesting.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.PerformanceTesting\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEditor.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEngine.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}