Using pre-set license
Built from '6000.0/release' branch; Version is '6000.0.43f1 (97272b72f107) revision 9905963'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 14229 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.43f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
S:/Unity Projects/Twistoria
-logFile
Logs/AssetImportWorker3.log
-srvPort
51176
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: S:/Unity Projects/Twistoria
S:/Unity Projects/Twistoria
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [13268]  Target information:

Player connection [13268]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 817621700 [EditorId] 817621700 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13268]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 817621700 [EditorId] 817621700 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13268]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 817621700 [EditorId] 817621700 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13268] Host joined multi-casting on [***********:54997]...
Player connection [13268] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.43f1 (97272b72f107)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path S:/Unity Projects/Twistoria/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) Graphics (ID=0x1638)
    Vendor:   ATI
    VRAM:     7114 MB
    Driver:   31.0.21923.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56048
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004751 seconds.
- Loaded All Assemblies, in  0.427 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.316 seconds
Domain Reload Profiling: 741ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (155ms)
		LoadAssemblies (156ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (150ms)
				TypeCache.ScanAssembly (138ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (316ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (273ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (49ms)
			ProcessInitializeOnLoadAttributes (144ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.759 seconds
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Twistoria
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.785 seconds
Domain Reload Profiling: 1541ms
	BeginReloadAssembly (172ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (496ms)
		LoadAssemblies (354ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (238ms)
			TypeCache.Refresh (179ms)
				TypeCache.ScanAssembly (162ms)
			BuildScriptInfoCaches (45ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (785ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (614ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (435ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 220 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6366 unused Assets / (4.3 MB). Loaded Objects now: 7109.
Memory consumption went from 190.6 MB to 186.3 MB.
Total: 16.135200 ms (FindLiveObjects: 1.600700 ms CreateObjectMapping: 1.254200 ms MarkObjects: 9.804200 ms  DeleteObjects: 3.473500 ms)

========================================================================
Received Import Request.
  Time since last request: 524770.680255 seconds.
  path: Assets/Low Poly Weapons VOL.1/Prefabs/TAN_LR_Scope_01.prefab
  artifactKey: Guid(49535f27af2648646a94c1f9918737d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Prefabs/TAN_LR_Scope_01.prefab using Guid(49535f27af2648646a94c1f9918737d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '900bf798a870e4d06bac53fc62a7c3bb') in 0.5269721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 19.017968 seconds.
  path: Assets/Low Poly Weapons VOL.1/Prefabs/ELCAN.prefab
  artifactKey: Guid(75aeffeed5a736f46afbef3a42e93fab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Prefabs/ELCAN.prefab using Guid(75aeffeed5a736f46afbef3a42e93fab) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '777707640fd81c595502fa73eb2dfa63') in 0.0266765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Low Poly Weapons VOL.1/Prefabs/M2_50cal.prefab
  artifactKey: Guid(3077a2b322d12b94c975bb1c7b2993fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Prefabs/M2_50cal.prefab using Guid(3077a2b322d12b94c975bb1c7b2993fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9e2352283a718f7a3afc3c86ade7cad') in 0.0199359 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 43

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Low Poly Weapons VOL.1/Prefabs/M107.prefab
  artifactKey: Guid(7359baf27e84aa44cafac3788c8be0f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Prefabs/M107.prefab using Guid(7359baf27e84aa44cafac3788c8be0f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '90f82f04fe4fd2f99ca80733909ccd7c') in 0.0204605 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Low Poly Weapons VOL.1/Prefabs/RPG7.prefab
  artifactKey: Guid(02baef62b1bfedf40bb369fb7542b9e7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Low Poly Weapons VOL.1/Prefabs/RPG7.prefab using Guid(02baef62b1bfedf40bb369fb7542b9e7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c0b2072b362461f00d74f26c3e967f84') in 0.0188796 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0