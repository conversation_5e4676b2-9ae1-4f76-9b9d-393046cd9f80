using UnityEngine;

namespace bullet.fx.pack {
    [RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
    public sealed class Cylinder : MonoBehaviour
    {
        [SerializeField] private float radius = 0.5f;
        [SerializeField] private int segments = 24;

        private void Start() {
            var customMesh = new Mesh();
            customMesh.name = "SimpleSphere";
            
            CreateSphere(customMesh);
            
            GetComponent<MeshFilter>().mesh = customMesh;
        }

        private void CreateSphere(Mesh mesh) {
            // Create arrays to hold the data
            Vector3[] vertices = new Vector3[(segments + 1) * (segments + 1)];
            int[] triangles = new int[segments * segments * 6];
            Vector2[] uv = new Vector2[vertices.Length];

            // Create vertices
            for (int lat = 0; lat <= segments; lat++) {
                float a1 = Mathf.PI * ((float)lat / segments - 0.5f);
                float sin1 = Mathf.Sin(a1);
                float cos1 = Mathf.Cos(a1);

                for (int lon = 0; lon <= segments; lon++) {
                    float a2 = 2 * Mathf.PI * (float)lon / segments;
                    float sin2 = Mathf.Sin(a2);
                    float cos2 = Mathf.Cos(a2);

                    vertices[lon + lat * (segments + 1)] = new Vector3(
                        sin1 * cos2 * radius,
                        cos1 * radius,
                        sin1 * sin2 * radius);

                    uv[lon + lat * (segments + 1)] = new Vector2((float)lon / segments, (float)lat / segments);
                }
            }

            // Create triangles
            int triIndex = 0;
            for (int lat = 0; lat < segments; lat++) {
                for (int lon = 0; lon < segments; lon++) {
                    int current = lon + lat * (segments + 1);
                    int next = current + (segments + 1);

                    triangles[triIndex++] = current;
                    triangles[triIndex++] = current + 1;
                    triangles[triIndex++] = next + 1;

                    triangles[triIndex++] = current;
                    triangles[triIndex++] = next + 1;
                    triangles[triIndex++] = next;
                }
            }

            mesh.vertices = vertices;
            mesh.triangles = triangles;
            mesh.uv = uv;
            mesh.RecalculateNormals();
        }

                new Vector3(-0.4f * coef2, -2.5f, 0),
                new Vector3(-radiusMediumHalf * coef2, -2.5f, radiusMediumHalf * coef2),
                new Vector3(0, -2.5f, 0.4f * coef2),
                new Vector3(radiusMediumHalf * coef2, -2.5f, radiusMediumHalf * coef2),
                new Vector3(0.4f * coef2, -2.5f, 0),
                new Vector3(radiusMediumHalf * coef2, -2.5f, -radiusMediumHalf * coef2),
                new Vector3(0, -2.5f, -0.4f * coef2),
                new Vector3(-radiusMediumHalf * coef2, -2.5f, -radiusMediumHalf * coef2),

                new Vector3(-0.4f * coef, -0.5f, 0),
                new Vector3(-radiusMediumHalf * coef, -0.5f, radiusMediumHalf * coef),
                new Vector3(0, -0.5f, 0.4f * coef),
                new Vector3(radiusMediumHalf * coef, -0.5f, radiusMediumHalf * coef),
                new Vector3(0.4f * coef, -0.5f, 0),
                new Vector3(radiusMediumHalf * coef, -0.5f, -radiusMediumHalf * coef),
                new Vector3(0, -0.5f, -0.4f * coef),
                new Vector3(-radiusMediumHalf * coef, -0.5f, -radiusMediumHalf * coef),
            };
        }

        private int[] SetTriangles() {
            return new int[]{
                8, 0, 1,
                8, 1, 9,
                9, 1, 2,
                9, 2, 10,
                10, 2, 3,
                10, 3, 11,
                11, 3, 4,
                11, 4, 12,
                12, 4, 5,
                12, 5, 13,
                13, 5, 6,
                13, 6, 14,
                14, 6, 7,
                14, 7, 15,
                15, 7, 8,
                8, 7, 0,
            };
        }

        private Vector2[] SetUVs(Vector3[] vertices) {
            Vector2[] uvs = new Vector2[vertices.Length];
            float height = 2.0f;

            for (int i = 0; i < vertices.Length; i++) {
                Vector3 vertex = vertices[i];

                if (vertex.y > 0) {
                    float u = Mathf.Atan2(vertex.z, vertex.x) / (2 * Mathf.PI);
                    float v = (vertex.y + height * 0.5f) / height;

                    uvs[i] = new Vector2(u, v);
                } else {
                    float u = Mathf.Atan2(vertex.z, vertex.x) / (2 * Mathf.PI);
                    float v = (vertex.y + height * 0.5f) / height;

                    uvs[i] = new Vector2(u, v);
                }
            }
            return uvs;
        }
    }
}