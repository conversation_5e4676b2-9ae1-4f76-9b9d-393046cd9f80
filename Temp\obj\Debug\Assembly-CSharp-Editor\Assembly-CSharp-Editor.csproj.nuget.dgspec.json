{"format": 1, "restore": {"S:\\Unity Projects\\Twistoria\\Assembly-CSharp-Editor.csproj": {}}, "projects": {"S:\\Unity Projects\\Twistoria\\Assembly-CSharp-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Assembly-CSharp-firstpass.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp-firstpass.csproj"}, "S:\\Unity Projects\\Twistoria\\Assembly-CSharp.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp.csproj"}, "S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj"}, "S:\\Unity Projects\\Twistoria\\AstarPathfindingProjectEditor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProjectEditor.csproj"}, "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj"}, "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj"}, "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj"}, "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Assembly-CSharp-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj"}, "S:\\Unity Projects\\Twistoria\\AstarPathfindingProjectEditor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProjectEditor.csproj"}, "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj"}, "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj"}, "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj"}, "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Assembly-CSharp-firstpass.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Assembly-CSharp-firstpass.csproj"}, "S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj"}, "S:\\Unity Projects\\Twistoria\\AstarPathfindingProjectEditor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProjectEditor.csproj"}, "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj"}, "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj"}, "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj"}, "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj", "projectName": "AstarPathfindingProject", "projectPath": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\AstarPathfindingProject\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\AstarPathfindingProjectEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProjectEditor.csproj", "projectName": "AstarPathfindingProjectEditor", "projectPath": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProjectEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\AstarPathfindingProjectEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj"}, "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj", "projectName": "Cinemachine", "projectPath": "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Cinemachine\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj", "projectName": "com.unity.cinemachine.editor", "projectPath": "S:\\Unity Projects\\Twistoria\\com.unity.cinemachine.editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\com.unity.cinemachine.editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Cinemachine.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Cinemachine.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj", "projectName": "PackageToolsEditor", "projectPath": "S:\\Unity Projects\\Twistoria\\PackageToolsEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\PackageToolsEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\AstarPathfindingProject.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj", "projectName": "PPv2URPConverters", "projectPath": "S:\\Unity Projects\\Twistoria\\PPv2URPConverters.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\PPv2URPConverters\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj", "projectName": "Unity.AI.Navigation", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.AI.Navigation\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj", "projectName": "Unity.AI.Navigation.Editor.ConversionSystem", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.AI.Navigation.Editor.ConversionSystem\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj", "projectName": "Unity.AI.Navigation.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.AI.Navigation.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj", "projectName": "Unity.AI.Navigation.Updater", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Updater.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.AI.Navigation.Updater\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.AI.Navigation.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj", "projectName": "Unity.Animation.Rigging", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Animation.Rigging\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj", "projectName": "Unity.Animation.Rigging.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Animation.Rigging.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Animation.Rigging.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj", "projectName": "Unity.Burst", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Burst\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj", "projectName": "Unity.Burst.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Burst.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj", "projectName": "Unity.Collections", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Collections\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj", "projectName": "Unity.Collections.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Collections.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj", "projectName": "Unity.InputSystem", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.InputSystem\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj", "projectName": "Unity.InputSystem.ForUI", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.ForUI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.InputSystem.ForUI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj", "projectName": "Unity.Mathematics", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Mathematics\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj", "projectName": "Unity.Mathematics.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Mathematics.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj", "projectName": "Unity.Multiplayer.Center.Common", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Multiplayer.Center.Common\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj", "projectName": "Unity.Multiplayer.Center.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Multiplayer.Center.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Multiplayer.Center.Common.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj", "projectName": "Unity.PackageValidationSuite.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.PackageValidationSuite.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj", "projectName": "Unity.PackageValidationSuite.Editor.Extension", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.Extension.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.PackageValidationSuite.Editor.Extension\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PackageValidationSuite.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj", "projectName": "Unity.PlasticSCM.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.PlasticSCM.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.PlasticSCM.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj", "projectName": "Unity.Rendering.LightTransport.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Rendering.LightTransport.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj", "projectName": "Unity.Rendering.LightTransport.Runtime", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Rendering.LightTransport.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "projectName": "Unity.RenderPipeline.Universal.ShaderLibrary", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipeline.Universal.ShaderLibrary\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj", "projectName": "Unity.RenderPipelines.Core.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Core.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rendering.LightTransport.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj", "projectName": "Unity.RenderPipelines.Core.Editor.Shared", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Core.Editor.Shared\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj", "projectName": "Unity.RenderPipelines.Core.Runtime", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Core.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj", "projectName": "Unity.RenderPipelines.Core.Runtime.Shared", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Core.Runtime.Shared\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj", "projectName": "Unity.RenderPipelines.Core.ShaderLibrary", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.ShaderLibrary.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Core.ShaderLibrary\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj", "projectName": "Unity.RenderPipelines.GPUDriven.Runtime", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.GPUDriven.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "projectName": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj", "projectName": "Unity.RenderPipelines.Universal.2D.Runtime", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Universal.2D.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj", "projectName": "Unity.RenderPipelines.Universal.Config.Runtime", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Universal.Config.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj", "projectName": "Unity.RenderPipelines.Universal.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Universal.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj", "projectName": "Unity.RenderPipelines.Universal.Runtime", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Universal.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj", "projectName": "Unity.RenderPipelines.Universal.Shaders", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Universal.Shaders.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Universal.Shaders\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj", "projectName": "Unity.Rider.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Rider.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Rider.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj", "projectName": "Unity.Searcher.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Searcher.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj", "projectName": "Unity.ShaderGraph.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.ShaderGraph.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Searcher.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Utilities.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Utilities.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Utilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Utilities.csproj", "projectName": "Unity.ShaderGraph.Utilities", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.ShaderGraph.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.ShaderGraph.Utilities\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj", "projectName": "Unity.TextMeshPro", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.TextMeshPro\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj", "projectName": "Unity.TextMeshPro.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.TextMeshPro.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.TextMeshPro.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj", "projectName": "Unity.Timeline", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Timeline\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj", "projectName": "Unity.Timeline.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Timeline.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Timeline.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj", "projectName": "Unity.VisualScripting.Core", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.VisualScripting.Core\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj", "projectName": "Unity.VisualScripting.Core.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.VisualScripting.Core.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj", "projectName": "Unity.VisualScripting.Flow", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.VisualScripting.Flow\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj", "projectName": "Unity.VisualScripting.Flow.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.VisualScripting.Flow.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj", "projectName": "Unity.VisualScripting.SettingsProvider.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.VisualScripting.SettingsProvider.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj", "projectName": "Unity.VisualScripting.Shared.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Shared.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.VisualScripting.Shared.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj", "projectName": "Unity.VisualScripting.State", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.VisualScripting.State\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj", "projectName": "Unity.VisualScripting.State.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.VisualScripting.State.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj", "projectName": "Unity.VisualStudio.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualStudio.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.VisualStudio.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEditor.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEngine.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}