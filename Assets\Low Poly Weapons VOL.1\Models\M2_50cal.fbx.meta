fileFormatVersion: 2
guid: 018d5bd3bfb8b6a49a8b354ba11e4a8f
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: 1
    100002: Bolt
    100004: CINEMA_4D_Editor
    100006: Gun
    100008: M2
    100010: //RootNode
    100012: Magazin
    100014: Slide
    100016: Trigger
    400000: 1
    400002: Bolt
    400004: CINEMA_4D_Editor
    400006: Gun
    400008: M2
    400010: //RootNode
    400012: Magazin
    400014: Slide
    400016: Trigger
    2000000: CINEMA_4D_Editor
    2100000: Mat
    2300000: 1
    2300002: Bolt
    2300004: Gun
    2300006: M2
    2300008: Magazin
    2300010: Slide
    2300012: Trigger
    3300000: 1
    3300002: Bolt
    3300004: Gun
    3300006: M2
    3300008: Magazin
    3300010: Slide
    3300012: Trigger
    4300000: M2
    4300002: 1
    4300004: Gun
    4300006: Magazin
    4300008: Bolt
    4300010: Trigger
    4300012: Slide
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Mat
    second: {fileID: 2100000, guid: 28f0b13c7e9707746b5e0b3336181560, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.9341716
    meshCompression: 0
    addColliders: 0
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 151980
  packageName: Untitled
  packageVersion: 1.0
  assetPath: Assets/Low Poly Weapons VOL.1/Models/M2_50cal.fbx
  uploadId: 324403
