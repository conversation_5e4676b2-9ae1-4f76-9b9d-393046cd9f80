using UnityEngine;
using UnityEngine.Animations.Rigging;

public class Weapon : MonoBehaviour
{
    public bool active = false;
    public GameObject[] guns;
    int active_gun = 0;
    [SerializeField] private Transform left_hand_target_transform;
    [SerializeField] private Transform left_hand_hint_transform;
    [SerializeField] private Transform right_hand_target_transform;
    [SerializeField] private Transform right_hand_hint_transform;
    private Transform left_hand_target_;
    private Transform left_hand_hint_;
    private Transform right_hand_target_;
    private Transform right_hand_hint_;
    public Transform shootPoints;
    public GameObject bulletPrefab;

    [SerializeField] private Rig rig;
    [SerializeField] private Animator animator;

    void Start()
    {
        if(guns.Length > 0) ToggleWeapon();
    }

    // Update is called once per frame
    void Update()
    {
        SwitchWeapon();

        if (left_hand_target_ != null && left_hand_hint_ != null && right_hand_target_ != null && right_hand_hint_ != null)
        {
            left_hand_target_transform.position = left_hand_target_.position;
            left_hand_hint_transform.position = left_hand_hint_.position;
            right_hand_target_transform.position = right_hand_target_.position;
            right_hand_hint_transform.position = right_hand_hint_.position;

            left_hand_target_transform.rotation = left_hand_target_.rotation;
            left_hand_hint_transform.rotation = left_hand_hint_.rotation;
            right_hand_target_transform.rotation = right_hand_target_.rotation;
            right_hand_hint_transform.rotation = right_hand_hint_.rotation;
        }

        Shoot();
    }

    public void UpdateShootPoints()
    {
        shootPoints = null;

        shootPoints = guns[active_gun].transform.Find("Shoot_Point").transform;
    }

    void Shoot()
    {
        if (Input.GetMouseButtonDown(0) && !active)
        {

            Rigidbody rb = Instantiate(bulletPrefab, shootPoints.position, shootPoints.rotation).GetComponent<Rigidbody>();
            rb.linearVelocity = rb.transform.forward * 15;
            
        }
    }

    void SwitchWeapon()
    {
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            active_gun = 0;
            SwitchWeaponFunc();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            active_gun = 1;
            SwitchWeaponFunc();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            active_gun = 2;
            SwitchWeaponFunc();
        }
        if (active_gun >= guns.Length)
        {
            active_gun = 0;
        }
        if (active_gun < 0)
        {
            active_gun = guns.Length - 1;
        }

        UpdateShootPoints();
    }

    void SwitchWeaponFunc()
    {
        for (int i = 0; i < guns.Length; i++)
        {
            guns[i].SetActive(false);
        }
        guns[active_gun].SetActive(true);
        left_hand_target_ = guns[active_gun].transform.Find("Weapn_Aim_Left_target").transform;
        left_hand_hint_ = guns[active_gun].transform.Find("Weapn_Aim_Left_hint").transform;
        right_hand_target_ = guns[active_gun].transform.Find("Weapn_Aim_Right_target").transform;
        right_hand_hint_ = guns[active_gun].transform.Find("Weapn_Aim_Right_hint").transform;
        UpdateShootPoints();
    }

    public void ToggleWeapon()
    {
        if(guns.Length == 0) return;
        if (active)
        {
            active = false;
            rig.weight = 1;
            animator.SetLayerWeight(1, 1);
            SwitchWeaponFunc();
        }
        else
        {
            active = true;
            rig.weight = 0;
            animator.SetLayerWeight(1, 0);
            for (int i = 0; i < guns.Length; i++)
            {
                guns[i].SetActive(false);
            }
        }
    }
}
