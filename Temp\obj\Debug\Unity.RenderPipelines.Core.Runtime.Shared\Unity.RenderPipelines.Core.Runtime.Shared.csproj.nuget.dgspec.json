{"format": 1, "restore": {"S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {}}, "projects": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj", "projectName": "Unity.Burst", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Burst\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj", "projectName": "Unity.Collections", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Collections\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj", "projectName": "Unity.InputSystem", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.InputSystem\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj", "projectName": "Unity.Mathematics", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.Mathematics\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj", "projectName": "Unity.RenderPipelines.Core.Runtime", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Core.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Burst.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Collections.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.InputSystem.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.Mathematics.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj", "projectName": "Unity.RenderPipelines.Core.Runtime.Shared", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.RenderPipelines.Core.Runtime.Shared\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.RenderPipelines.Core.Runtime.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEditor.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\UnityEngine.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}