{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Unity.InputSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.dll": {}}}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"Unity.InputSystem/1.0.0": {"type": "project", "path": "Unity.InputSystem.csproj", "msbuildProject": "Unity.InputSystem.csproj"}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.csproj", "msbuildProject": "Unity.VisualScripting.Core.csproj"}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Core.Editor.csproj"}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.csproj", "msbuildProject": "Unity.VisualScripting.Flow.csproj"}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Flow.Editor.csproj"}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.csproj", "msbuildProject": "Unity.VisualScripting.State.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Unity.VisualScripting.Core >= 1.0.0", "Unity.VisualScripting.Core.Editor >= 1.0.0", "Unity.VisualScripting.Flow >= 1.0.0", "Unity.VisualScripting.Flow.Editor >= 1.0.0", "Unity.VisualScripting.State >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj", "projectName": "Unity.VisualScripting.SettingsProvider.Editor", "projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.SettingsProvider.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Unity Projects\\Twistoria\\Temp\\obj\\Debug\\Unity.VisualScripting.SettingsProvider.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Core.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.Flow.Editor.csproj"}, "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\Unity.VisualScripting.State.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEditor.UI.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.TestRunner.csproj"}, "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj": {"projectPath": "S:\\Unity Projects\\Twistoria\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}