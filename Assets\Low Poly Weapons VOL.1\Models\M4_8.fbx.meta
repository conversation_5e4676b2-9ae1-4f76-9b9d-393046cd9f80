fileFormatVersion: 2
guid: c7741850eebe1df4ea75a80275b16311
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bolt
    100002: //RootNode
    100004: Mag
    100006: Rear_Sight
    100008: Sight
    100010: Trigger
    400000: Bolt
    400002: //RootNode
    400004: Mag
    400006: Rear_Sight
    400008: Sight
    400010: Trigger
    2100000: Mat
    2300000: Bolt
    2300002: //RootNode
    2300004: Mag
    2300006: Rear_Sight
    2300008: Sight
    2300010: Trigger
    3300000: Bolt
    3300002: //RootNode
    3300004: Mag
    3300006: Rear_Sight
    3300008: Sight
    3300010: Trigger
    4300000: M4_8
    4300002: Mag
    4300004: Rear_Sight
    4300006: Trigger
    4300008: Bolt
    4300010: Sight
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Mat
    second: {fileID: 2100000, guid: 28f0b13c7e9707746b5e0b3336181560, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.09846544
    meshCompression: 0
    addColliders: 0
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 151980
  packageName: Untitled
  packageVersion: 1.0
  assetPath: Assets/Low Poly Weapons VOL.1/Models/M4_8.fbx
  uploadId: 324403
