Using pre-set license
Built from '6000.0/release' branch; Version is '6000.0.43f1 (97272b72f107) revision 9905963'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 14229 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.43f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
S:/Unity Projects/Twistoria
-logFile
Logs/AssetImportWorker3.log
-srvPort
62451
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: S:/Unity Projects/Twistoria
S:/Unity Projects/Twistoria
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [24748]  Target information:

Player connection [24748]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 4069679383 [EditorId] 4069679383 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24748]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4069679383 [EditorId] 4069679383 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24748]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4069679383 [EditorId] 4069679383 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NH07OKI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24748] Host joined multi-casting on [***********:54997]...
Player connection [24748] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 176.23 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.43f1 (97272b72f107)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path S:/Unity Projects/Twistoria/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) Graphics (ID=0x1638)
    Vendor:   ATI
    VRAM:     7114 MB
    Driver:   31.0.21923.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56020
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005482 seconds.
- Loaded All Assemblies, in  1.044 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.383 seconds
Domain Reload Profiling: 1426ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (748ms)
		LoadAssemblies (188ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (743ms)
			TypeCache.Refresh (741ms)
				TypeCache.ScanAssembly (725ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (384ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (332ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (168ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  1.031 seconds
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Twistoria
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.004 seconds
Domain Reload Profiling: 2033ms
	BeginReloadAssembly (187ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (749ms)
		LoadAssemblies (549ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (315ms)
			TypeCache.Refresh (252ms)
				TypeCache.ScanAssembly (181ms)
			BuildScriptInfoCaches (48ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1005ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (709ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (516ms)
			ProcessInitializeOnLoadMethodAttributes (84ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 1.04 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 227 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6529 unused Assets / (4.5 MB). Loaded Objects now: 7280.
Memory consumption went from 198.3 MB to 193.8 MB.
Total: 10.200500 ms (FindLiveObjects: 1.236400 ms CreateObjectMapping: 1.076900 ms MarkObjects: 5.284900 ms  DeleteObjects: 2.599700 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  1.017 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.341 seconds
Domain Reload Profiling: 2362ms
	BeginReloadAssembly (243ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (690ms)
		LoadAssemblies (454ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (363ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (326ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1342ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1012ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (171ms)
			ProcessInitializeOnLoadAttributes (731ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6528 unused Assets / (5.2 MB). Loaded Objects now: 7296.
Memory consumption went from 176.1 MB to 171.0 MB.
Total: 21.860400 ms (FindLiveObjects: 1.891600 ms CreateObjectMapping: 2.003400 ms MarkObjects: 11.593900 ms  DeleteObjects: 6.368900 ms)

Prepare: number of updated asset objects reloaded= 0
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0