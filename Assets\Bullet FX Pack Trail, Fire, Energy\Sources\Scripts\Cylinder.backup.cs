using UnityEngine;

namespace bullet.fx.pack {
    [RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
    public sealed class Cylinder : MonoBehaviour
    {
        [SerializeField] private float radius = 0.5f;
        [SerializeField] private int segments = 24;

        private void Start() {
            var customMesh = new Mesh();
            customMesh.name = "SimpleSphere";
            
            CreateSphere(customMesh);
            
            GetComponent<MeshFilter>().mesh = customMesh;
        }

        private void CreateSphere(Mesh mesh) {
            // Create arrays to hold the data
            Vector3[] vertices = new Vector3[(segments + 1) * (segments + 1)];
            int[] triangles = new int[segments * segments * 6];
            Vector2[] uv = new Vector2[vertices.Length];

            // Create vertices
            for (int lat = 0; lat <= segments; lat++) {
                float a1 = Mathf.PI * ((float)lat / segments - 0.5f);
                float sin1 = Mathf.Sin(a1);
                float cos1 = Mathf.Cos(a1);

                for (int lon = 0; lon <= segments; lon++) {
                    float a2 = 2 * Mathf.PI * (float)lon / segments;
                    float sin2 = Mathf.Sin(a2);
                    float cos2 = Mathf.Cos(a2);

                    vertices[lon + lat * (segments + 1)] = new Vector3(
                        sin1 * cos2 * radius,
                        cos1 * radius,
                        sin1 * sin2 * radius);

                    uv[lon + lat * (segments + 1)] = new Vector2((float)lon / segments, (float)lat / segments);
                }
            }

            // Create triangles
            int triIndex = 0;
            for (int lat = 0; lat < segments; lat++) {
                for (int lon = 0; lon < segments; lon++) {
                    int current = lon + lat * (segments + 1);
                    int next = current + (segments + 1);

                    triangles[triIndex++] = current;
                    triangles[triIndex++] = current + 1;
                    triangles[triIndex++] = next + 1;

                    triangles[triIndex++] = current;
                    triangles[triIndex++] = next + 1;
                    triangles[triIndex++] = next;
                }
            }

            mesh.vertices = vertices;
            mesh.triangles = triangles;
            mesh.uv = uv;
            mesh.RecalculateNormals();
        }
    }
}
