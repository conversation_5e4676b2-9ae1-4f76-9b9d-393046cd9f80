using UnityEngine;

public class Health : MonoBehaviour
{
    [SerializeField] private float max_health = 100f;
    [SerializeField] private float current_health = 0f;

    void Awake()
    {
        current_health = max_health;
    }

    public void TakeDamage(float damage)
    {
        current_health -= damage;
        if (current_health <= 0) Die();
    }

    void Die()
    {
        Destroy(gameObject);
    }
}
